import { ref, reactive } from 'vue'
import { adminAPI } from '@/services/api'

interface Stats {
  totalApplications: number
  pendingApplications: number
  processingApplications: number
  completedApplications: number
  totalUsers: number
  activeUsers: number
  totalRevenue: number
  monthlyRevenue: number
}

export function useAdminStats() {
  const loading = ref(false)
  const stats = reactive<Stats>({
    totalApplications: 0,
    pendingApplications: 0,
    processingApplications: 0,
    completedApplications: 0,
    totalUsers: 0,
    activeUsers: 0,
    totalRevenue: 0,
    monthlyRevenue: 0
  })

  const fetchStats = async (): Promise<void> => {
    try {
      loading.value = true
      const response = await adminAPI.getDashboardStats()

      if (response.data && response.data.stats) {
        // Map the API response to our stats structure
        const data = response.data.stats
        stats.totalApplications = data.applications?.total || 0
        stats.pendingApplications = data.applications?.pending || 0
        stats.processingApplications = data.applications?.processing || 0
        stats.completedApplications = data.applications?.completed || 0
        stats.totalUsers = data.users?.total || 0
        stats.activeUsers = data.users?.user || 0
        stats.totalRevenue = 0 // Not provided by API yet
        stats.monthlyRevenue = 0 // Not provided by API yet
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    stats,
    loading,
    fetchStats
  }
}

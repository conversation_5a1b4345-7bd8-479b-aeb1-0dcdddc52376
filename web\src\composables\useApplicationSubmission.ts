import { ref, reactive, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { applicationAPI } from '@/services/apiService'

interface ApplicationFormData {
  // 蛋白组Panel信息
  proteinPanel: string
  proteinPanelOption: string
  otherPlatformName: string
  sampleCount: number | null

  // 联系信息
  phone: string
  contactName: string
  contactOrganization: string

  // 项目详情
  projectName: string
  sampleOrigin: string
  sampleType: string
  otherSampleType: string
  contractNumber: string
  notes: string

  // 文件信息
  proteinFiles: any[]
  groupFiles: any[]
}

interface ValidationErrors {
  [key: string]: string
}

export function useApplicationSubmission() {
  const { t } = useI18n()

  // 表单数据
  const formData = reactive<ApplicationFormData>({
    // 蛋白组Panel信息
    proteinPanel: '',
    proteinPanelOption: '',
    otherPlatformName: '',
    sampleCount: null,

    // 联系信息
    phone: '',
    contactName: '',
    contactOrganization: '',

    // 项目详情
    projectName: '',
    sampleOrigin: '',
    sampleType: '',
    otherSampleType: '',
    contractNumber: '',
    notes: '',

    // 文件信息
    proteinFiles: [],
    groupFiles: []
  })

  // 状态管理
  const submitting = ref(false)
  const errors = ref<ValidationErrors>({})
  const successMessage = ref<string | null>(null)

  // 验证规则
  const validateProteinPanel = () => {
    const panelErrors: ValidationErrors = {}

    if (!formData.proteinPanel) {
      panelErrors.proteinPanel = t('proteinPanelRequired')
    }

    if (!formData.sampleCount || formData.sampleCount <= 0) {
      panelErrors.sampleCount = t('sampleCountRequired')
    }

    if (formData.proteinPanel === 'other' && !formData.otherPlatformName) {
      panelErrors.otherPlatformName = t('otherPlatformNameRequired')
    }

    return panelErrors
  }

  const validateContact = () => {
    const contactErrors: ValidationErrors = {}

    if (!formData.phone) {
      contactErrors.phone = t('contactPhoneRequired')
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      contactErrors.phone = t('contactPhoneInvalid')
    }

    return contactErrors
  }

  const validateProject = () => {
    const projectErrors: ValidationErrors = {}

    if (formData.sampleType === 'other' && !formData.otherSampleType) {
      projectErrors.otherSampleType = t('otherSampleTypeRequired')
    }

    return projectErrors
  }

  const validateFiles = () => {
    const fileErrors: ValidationErrors = {}

    if (formData.proteinFiles.length === 0) {
      fileErrors.proteinFiles = t('proteinFilesRequired')
    }

    return fileErrors
  }

  // 完整表单验证
  const validateForm = () => {
    const allErrors = {
      ...validateProteinPanel(),
      ...validateContact(),
      ...validateProject(),
      ...validateFiles()
    }

    errors.value = allErrors
    return Object.keys(allErrors).length === 0
  }

  // 表单验证状态
  const isFormValid = computed(() => {
    return (
      formData.proteinPanel &&
      formData.sampleCount &&
      formData.sampleCount > 0 &&
      formData.proteinFiles.length > 0 &&
      formData.phone &&
      (formData.proteinPanel !== 'other' || formData.otherPlatformName) &&
      (formData.sampleType !== 'other' || formData.otherSampleType)
    )
  })

  // 提交申请
  const submitApplication = async () => {
    if (!validateForm()) {
      throw new Error(t('formValidationFailed'))
    }

    submitting.value = true
    errors.value = {}
    successMessage.value = null

    try {
      // 准备提交数据
      const submitData = {
        phone: formData.phone,
        contactName: formData.contactName,
        contactOrganization: formData.contactOrganization,
        proteinPanel: formData.proteinPanel,
        proteinPanelOption: formData.proteinPanelOption,
        otherPlatformName: formData.otherPlatformName,
        sampleCount: formData.sampleCount,
        projectName: formData.projectName,
        sampleOrigin: formData.sampleOrigin,
        sampleType: formData.sampleType,
        otherSampleType: formData.otherSampleType,
        contractNumber: formData.contractNumber,
        notes: formData.notes,
        proteinDataFiles: formData.proteinFiles.map(f => f.filename),
        sampleGroupFiles: formData.groupFiles.map(f => f.filename)
      }

      const response = await applicationAPI.createApplication(submitData)
      
      if (response.data.success) {
        successMessage.value = t('applicationSubmittedSuccessfully')
        resetForm()
        return response.data.application
      } else {
        throw new Error(response.data.message || t('applicationSubmissionFailed'))
      }
    } catch (err: any) {
      console.error('Failed to submit application:', err)

      // 处理详细的验证错误
      if (err.response?.data?.details && err.response.data.details.length > 0) {
        const fieldErrors: ValidationErrors = {}
        err.response.data.details.forEach((detail: any) => {
          const field = detail.path || detail.param
          fieldErrors[field] = detail.msg
        })
        errors.value = fieldErrors
      } else {
        const errorMessage = err.response?.data?.message || err.message || t('applicationSubmissionFailed')
        errors.value = { general: errorMessage }
      }
      
      throw err
    } finally {
      submitting.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      proteinPanel: '',
      proteinPanelOption: '',
      otherPlatformName: '',
      sampleCount: null,
      phone: '',
      contactName: '',
      contactOrganization: '',
      projectName: '',
      sampleOrigin: '',
      sampleType: '',
      otherSampleType: '',
      contractNumber: '',
      notes: '',
      proteinFiles: [],
      groupFiles: []
    })
    errors.value = {}
    successMessage.value = null
  }

  // 更新表单数据
  const updateFormData = (field: keyof ApplicationFormData, value: any) => {
    ;(formData as any)[field] = value
    
    // 清除相关字段的错误
    if (errors.value[field]) {
      delete errors.value[field]
    }
  }

  // 批量更新表单数据
  const updateFormSection = (section: Partial<ApplicationFormData>) => {
    Object.assign(formData, section)
    
    // 清除相关错误
    Object.keys(section).forEach(key => {
      if (errors.value[key]) {
        delete errors.value[key]
      }
    })
  }

  // 获取表单摘要
  const getFormSummary = () => {
    return {
      hasProteinPanel: !!formData.proteinPanel,
      hasContact: !!formData.phone,
      hasFiles: formData.proteinFiles.length > 0,
      isValid: isFormValid.value,
      errorCount: Object.keys(errors.value).length
    }
  }

  return {
    // 状态
    formData,
    submitting,
    errors,
    successMessage,
    isFormValid,

    // 方法
    validateForm,
    validateProteinPanel,
    validateContact,
    validateProject,
    validateFiles,
    submitApplication,
    resetForm,
    updateFormData,
    updateFormSection,
    getFormSummary
  }
}

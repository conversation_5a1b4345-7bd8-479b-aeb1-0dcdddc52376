import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { uploadAPI } from '@/services/apiService'

interface UploadedFile {
  filename: string
  originalName: string
  size: number
  uploadedAt?: string
}

interface FileUploadState {
  proteinFiles: UploadedFile[]
  groupFiles: UploadedFile[]
}

interface UseFileUploadOptions {
  maxFiles?: number
  maxFileSize?: number // in bytes
  allowedTypes?: string[]
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const { t } = useI18n()

  // 默认配置
  const config = {
    maxFiles: options.maxFiles || 5,
    maxFileSize: options.maxFileSize || 100 * 1024 * 1024, // 100MB
    allowedTypes: options.allowedTypes || ['.xlsx', '.xls', '.csv', '.txt']
  }

  // 状态管理
  const files = reactive<FileUploadState>({
    proteinFiles: [],
    groupFiles: []
  })
  
  const uploading = ref(false)
  const error = ref<string | null>(null)

  // 验证文件
  const validateFiles = (fileList: FileList, type: 'protein' | 'group') => {
    const currentFiles = type === 'protein' ? files.proteinFiles : files.groupFiles
    
    // 检查文件数量限制
    if (currentFiles.length + fileList.length > config.maxFiles) {
      throw new Error(t('maxFilesExceeded', { max: config.maxFiles }))
    }

    // 检查每个文件
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i]
      
      // 检查文件大小
      if (file.size > config.maxFileSize) {
        throw new Error(t('fileSizeExceeded', { 
          filename: file.name, 
          maxSize: formatFileSize(config.maxFileSize) 
        }))
      }

      // 检查文件类型
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!config.allowedTypes.includes(fileExtension)) {
        throw new Error(t('fileTypeNotAllowed', { 
          filename: file.name, 
          allowedTypes: config.allowedTypes.join(', ') 
        }))
      }

      // 检查重复文件名
      const isDuplicate = currentFiles.some(f => f.originalName === file.name)
      if (isDuplicate) {
        throw new Error(t('duplicateFileName', { filename: file.name }))
      }
    }
  }

  // 上传文件
  const uploadFiles = async (fileList: FileList, type: 'protein' | 'group') => {
    uploading.value = true
    error.value = null

    try {
      // 验证文件
      validateFiles(fileList, type)

      // 创建FormData
      const formData = new FormData()
      Array.from(fileList).forEach((file) => {
        formData.append('files', file)
      })

      // 上传文件
      const response = await uploadAPI.uploadFiles(formData)
      const uploadedFiles = response.data.files || []

      // 更新状态
      if (type === 'protein') {
        files.proteinFiles.push(...uploadedFiles)
      } else {
        files.groupFiles.push(...uploadedFiles)
      }

      return uploadedFiles
    } catch (err: any) {
      const errorMessage = err.message || err.response?.data?.message || t('uploadError')
      error.value = errorMessage
      console.error('Failed to upload files:', err)
      throw new Error(errorMessage)
    } finally {
      uploading.value = false
    }
  }

  // 移除文件
  const removeFile = (filename: string, type: 'protein' | 'group') => {
    try {
      if (type === 'protein') {
        const index = files.proteinFiles.findIndex(f => f.filename === filename)
        if (index !== -1) {
          files.proteinFiles.splice(index, 1)
        }
      } else {
        const index = files.groupFiles.findIndex(f => f.filename === filename)
        if (index !== -1) {
          files.groupFiles.splice(index, 1)
        }
      }
      return true
    } catch (err: any) {
      const errorMessage = t('removeFileError')
      error.value = errorMessage
      console.error('Failed to remove file:', err)
      throw new Error(errorMessage)
    }
  }

  // 清空所有文件
  const clearFiles = (type?: 'protein' | 'group') => {
    if (type === 'protein') {
      files.proteinFiles = []
    } else if (type === 'group') {
      files.groupFiles = []
    } else {
      files.proteinFiles = []
      files.groupFiles = []
    }
  }

  // 获取文件列表
  const getFiles = (type: 'protein' | 'group') => {
    return type === 'protein' ? files.proteinFiles : files.groupFiles
  }

  // 检查是否有文件
  const hasFiles = (type?: 'protein' | 'group') => {
    if (type === 'protein') {
      return files.proteinFiles.length > 0
    } else if (type === 'group') {
      return files.groupFiles.length > 0
    } else {
      return files.proteinFiles.length > 0 || files.groupFiles.length > 0
    }
  }

  // 获取文件总数
  const getFileCount = (type?: 'protein' | 'group') => {
    if (type === 'protein') {
      return files.proteinFiles.length
    } else if (type === 'group') {
      return files.groupFiles.length
    } else {
      return files.proteinFiles.length + files.groupFiles.length
    }
  }

  // 工具方法
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 验证必需的文件是否已上传
  const validateRequiredFiles = () => {
    if (files.proteinFiles.length === 0) {
      throw new Error(t('proteinFilesRequired'))
    }
    return true
  }

  // 获取上传进度信息
  const getUploadInfo = () => {
    return {
      proteinFilesCount: files.proteinFiles.length,
      groupFilesCount: files.groupFiles.length,
      totalFiles: files.proteinFiles.length + files.groupFiles.length,
      maxFiles: config.maxFiles,
      uploading: uploading.value,
      hasError: !!error.value
    }
  }

  return {
    // 状态
    files,
    uploading,
    error,
    config,

    // 方法
    uploadFiles,
    removeFile,
    clearFiles,
    getFiles,
    hasFiles,
    getFileCount,
    validateRequiredFiles,
    getUploadInfo,

    // 工具方法
    formatFileSize
  }
}

const { migrateApplicationFields } = require('./migrate-application-fields');
const { addApplicationIndexes } = require('./add-application-indexes');
const { sequelize } = require('../models');
require('dotenv').config();

/**
 * 完整的Application表升级脚本
 * 
 * 此脚本将执行以下操作：
 * 1. 添加新的字段到Application表
 * 2. 为现有记录设置默认值
 * 3. 添加适当的数据库索引
 * 4. 验证升级结果
 * 
 * 使用方法：
 * node scripts/complete-application-upgrade.js [--dry-run] [--force]
 * 
 * 选项：
 * --dry-run: 预览模式，不实际执行修改
 * --force: 强制执行，跳过确认提示
 */

class ApplicationUpgrader {
  constructor(options = {}) {
    this.dryRun = options.dryRun || false;
    this.force = options.force || false;
    this.backupCreated = false;
  }

  async run() {
    try {
      console.log('🚀 开始Application表完整升级...');
      console.log(`📋 运行模式: ${this.dryRun ? '预览模式 (不会修改数据)' : '实际执行模式'}`);
      
      if (this.dryRun) {
        console.log('⚠️  预览模式：将显示要执行的操作，但不会实际修改数据库');
      }
      
      // 检查数据库连接
      await this.checkDatabaseConnection();
      
      // 创建备份（如果不是预览模式）
      if (!this.dryRun) {
        await this.createBackup();
      }
      
      // 分析当前状态
      await this.analyzeCurrentState();
      
      // 确认执行（如果不是强制模式且不是预览模式）
      if (!this.dryRun && !this.force) {
        await this.confirmExecution();
      }
      
      // 执行字段迁移
      await this.executeFieldMigration();
      
      // 执行索引添加
      await this.executeIndexAddition();
      
      // 验证升级结果
      await this.validateUpgrade();
      
      console.log('🎉 Application表升级完成！');
      
    } catch (error) {
      console.error('💥 升级过程中发生错误:', error.message);
      console.error('详细错误信息:', error);
      
      if (this.backupCreated) {
        console.log('💡 如果需要回滚，请使用之前创建的备份文件');
      }
      
      throw error;
    }
  }

  async checkDatabaseConnection() {
    console.log('🔍 检查数据库连接...');
    
    try {
      await sequelize.authenticate();
      console.log('✅ 数据库连接成功');
      
      // 检查Application表是否存在
      const queryInterface = sequelize.getQueryInterface();
      const tables = await queryInterface.showAllTables();
      
      if (!tables.includes('applications')) {
        throw new Error('applications表不存在，请先运行数据库初始化脚本');
      }
      
      console.log('✅ applications表存在');
      
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async createBackup() {
    console.log('💾 创建数据库备份...');
    
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const backupFile = `applications-backup-${timestamp}.sql`;
      
      // 这里应该实现实际的备份逻辑
      // 对于MySQL，可以使用mysqldump
      // 对于SQLite，可以复制数据库文件
      
      console.log(`📁 备份文件: ${backupFile}`);
      console.log('⚠️  请确保在升级前手动创建数据库备份');
      console.log('   MySQL: mysqldump -u username -p quantix > backup.sql');
      console.log('   SQLite: cp database.sqlite backup.sqlite');
      
      this.backupCreated = true;
      
    } catch (error) {
      console.error('❌ 备份创建失败:', error.message);
      throw error;
    }
  }

  async analyzeCurrentState() {
    console.log('📊 分析当前数据库状态...');
    
    try {
      const queryInterface = sequelize.getQueryInterface();
      
      // 获取当前表结构
      const tableDescription = await queryInterface.describeTable('applications');
      const currentFields = Object.keys(tableDescription);
      
      // 获取现有记录数量
      const [results] = await sequelize.query('SELECT COUNT(*) as count FROM applications');
      const recordCount = results[0].count;
      
      // 检查新字段
      const newFields = [
        'proteinPanel', 'proteinPanelOption', 'otherPlatformName',
        'sampleCount', 'projectName', 'sampleOrigin',
        'contactName', 'contactOrganization', 'sampleType',
        'otherSampleType', 'contractNumber', 'proteinDataFiles', 'sampleGroupFiles'
      ];
      
      const missingFields = newFields.filter(field => !currentFields.includes(field));
      const existingNewFields = newFields.filter(field => currentFields.includes(field));
      
      console.log(`📋 当前表状态:`);
      console.log(`   总字段数: ${currentFields.length}`);
      console.log(`   记录数量: ${recordCount}`);
      console.log(`   缺失的新字段 (${missingFields.length}): ${missingFields.join(', ')}`);
      
      if (existingNewFields.length > 0) {
        console.log(`   已存在的新字段 (${existingNewFields.length}): ${existingNewFields.join(', ')}`);
      }
      
      // 获取现有索引
      const indexes = await queryInterface.showIndex('applications');
      console.log(`   现有索引数量: ${indexes.length}`);
      
      return {
        recordCount,
        missingFields,
        existingNewFields,
        indexCount: indexes.length
      };
      
    } catch (error) {
      console.error('❌ 状态分析失败:', error.message);
      throw error;
    }
  }

  async confirmExecution() {
    console.log('\n⚠️  即将执行数据库升级操作');
    console.log('   此操作将修改数据库结构，请确保已创建备份');
    
    // 在实际项目中，这里应该实现用户输入确认
    // 为了自动化，这里跳过确认步骤
    console.log('✅ 确认执行升级操作');
  }

  async executeFieldMigration() {
    console.log('\n🔧 执行字段迁移...');
    
    if (this.dryRun) {
      console.log('📋 预览：将添加新字段到applications表');
      return;
    }
    
    try {
      await migrateApplicationFields();
      console.log('✅ 字段迁移完成');
    } catch (error) {
      console.error('❌ 字段迁移失败:', error.message);
      throw error;
    }
  }

  async executeIndexAddition() {
    console.log('\n🔧 执行索引添加...');
    
    if (this.dryRun) {
      console.log('📋 预览：将为新字段添加数据库索引');
      return;
    }
    
    try {
      await addApplicationIndexes();
      console.log('✅ 索引添加完成');
    } catch (error) {
      console.error('❌ 索引添加失败:', error.message);
      throw error;
    }
  }

  async validateUpgrade() {
    console.log('\n🔍 验证升级结果...');
    
    if (this.dryRun) {
      console.log('📋 预览模式：跳过验证');
      return;
    }
    
    try {
      const queryInterface = sequelize.getQueryInterface();
      
      // 验证字段
      const tableDescription = await queryInterface.describeTable('applications');
      const requiredFields = ['proteinPanel', 'sampleCount', 'proteinDataFiles', 'sampleGroupFiles'];
      
      for (const field of requiredFields) {
        if (!tableDescription[field]) {
          throw new Error(`必需字段 ${field} 未找到`);
        }
      }
      
      // 验证索引
      const indexes = await queryInterface.showIndex('applications');
      const indexNames = indexes.map(index => index.name);
      
      const requiredIndexes = ['applications_protein_panel', 'applications_sample_count'];
      for (const indexName of requiredIndexes) {
        if (!indexNames.includes(indexName)) {
          console.log(`⚠️  索引 ${indexName} 未找到，但这不会影响功能`);
        }
      }
      
      console.log('✅ 升级验证通过');
      
    } catch (error) {
      console.error('❌ 升级验证失败:', error.message);
      throw error;
    }
  }
}

// 解析命令行参数
function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    force: args.includes('--force')
  };
  
  return options;
}

// 如果直接运行此脚本
if (require.main === module) {
  const options = parseArguments();
  const upgrader = new ApplicationUpgrader(options);
  
  upgrader.run()
    .then(() => {
      console.log('\n🎉 升级脚本执行完成！');
      console.log('💡 建议运行应用程序测试以确保所有功能正常工作');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 升级脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { ApplicationUpgrader };

/**
 * End-to-End Test for Report Completion Feature
 * 
 * This test validates the complete workflow from application submission
 * to report completion with new fields (preview data file, complete data file,
 * actual sample count, actual protein count).
 * 
 * Test Flow:
 * 1. User submits application with all required fields
 * 2. Admin completes report with new fields
 * 3. User views completed report with new data
 * 4. Verify all new fields are properly displayed and functional
 */

const request = require('supertest');
const fs = require('fs');
const path = require('path');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const TEST_FILES_DIR = path.join(__dirname, 'test-files');

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

const testAdmin = {
  email: '<EMAIL>',
  password: 'AdminPassword123!'
};

const testApplication = {
  analysisType: 'protein_complex',
  proteinPanel: 'olink_explore_1536',
  sampleQuantity: 50,
  projectName: 'E2E Test Project',
  sampleSource: 'Human Plasma',
  contactName: 'Test Contact',
  contactEmail: '<EMAIL>',
  contactPhone: '+1234567890',
  sampleType: 'plasma',
  contractNumber: 'CONTRACT-2024-001'
};

const testReportCompletion = {
  reportPrice: 299.99,
  paymentStatus: 'unpaid',
  actualSampleCount: 48,
  actualProteinCount: 1520,
  notes: 'E2E test completion'
};

// Helper functions
function createTestFile(filename, content) {
  if (!fs.existsSync(TEST_FILES_DIR)) {
    fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
  }
  const filePath = path.join(TEST_FILES_DIR, filename);
  fs.writeFileSync(filePath, content);
  return filePath;
}

function cleanupTestFiles() {
  if (fs.existsSync(TEST_FILES_DIR)) {
    fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
  }
}

// Test suite
describe('End-to-End Report Completion Test', () => {
  let userToken, adminToken, applicationId;
  let previewReportFile, fullReportFile, previewDataFile, completeDataFile;

  beforeAll(async () => {
    // Create test files
    previewReportFile = createTestFile('preview-report.pdf', 'Mock preview report content');
    fullReportFile = createTestFile('full-report.pdf', 'Mock full report content');
    previewDataFile = createTestFile('preview-data.csv', 'sample_id,protein_1,protein_2\nS001,100,200\nS002,150,250');
    completeDataFile = createTestFile('complete-data.csv', 'sample_id,protein_1,protein_2,protein_3\nS001,100,200,300\nS002,150,250,350');
  });

  afterAll(async () => {
    // Cleanup test files
    cleanupTestFiles();
  });

  test('1. User Registration and Login', async () => {
    // Register test user
    const registerResponse = await request(API_BASE_URL)
      .post('/api/auth/register')
      .send(testUser);
    
    expect(registerResponse.status).toBe(201);

    // Login test user
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });
    
    expect(loginResponse.status).toBe(200);
    expect(loginResponse.body.token).toBeDefined();
    userToken = loginResponse.body.token;
  });

  test('2. Admin Login', async () => {
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/admin/login')
      .send(testAdmin);
    
    expect(loginResponse.status).toBe(200);
    expect(loginResponse.body.token).toBeDefined();
    adminToken = loginResponse.body.token;
  });

  test('3. User Submits Application', async () => {
    const response = await request(API_BASE_URL)
      .post('/api/applications')
      .set('Authorization', `Bearer ${userToken}`)
      .field('analysisType', testApplication.analysisType)
      .field('proteinPanel', testApplication.proteinPanel)
      .field('sampleQuantity', testApplication.sampleQuantity)
      .field('projectName', testApplication.projectName)
      .field('sampleSource', testApplication.sampleSource)
      .field('contactName', testApplication.contactName)
      .field('contactEmail', testApplication.contactEmail)
      .field('contactPhone', testApplication.contactPhone)
      .field('sampleType', testApplication.sampleType)
      .field('contractNumber', testApplication.contractNumber)
      .attach('proteinDataFiles', previewDataFile)
      .attach('sampleGroupFiles', completeDataFile);
    
    expect(response.status).toBe(201);
    expect(response.body.application).toBeDefined();
    expect(response.body.application.id).toBeDefined();
    applicationId = response.body.application.id;
  });

  test('4. Admin Completes Report with New Fields', async () => {
    const response = await request(API_BASE_URL)
      .post(`/api/admin/applications/${applicationId}/complete`)
      .set('Authorization', `Bearer ${adminToken}`)
      .field('reportPrice', testReportCompletion.reportPrice)
      .field('paymentStatus', testReportCompletion.paymentStatus)
      .field('actualSampleCount', testReportCompletion.actualSampleCount)
      .field('actualProteinCount', testReportCompletion.actualProteinCount)
      .field('notes', testReportCompletion.notes)
      .attach('previewReport', previewReportFile)
      .attach('fullReport', fullReportFile)
      .attach('previewDataFile', previewDataFile)
      .attach('completeDataFile', completeDataFile);
    
    expect(response.status).toBe(200);
    expect(response.body.application.status).toBe('completed');
    expect(response.body.application.actualSampleCount).toBe(testReportCompletion.actualSampleCount);
    expect(response.body.application.actualProteinCount).toBe(testReportCompletion.actualProteinCount);
    expect(response.body.application.previewDataFile).toBeDefined();
    expect(response.body.application.completeDataFile).toBeDefined();
  });

  test('5. User Views Completed Application with New Fields', async () => {
    const response = await request(API_BASE_URL)
      .get(`/api/applications/${applicationId}`)
      .set('Authorization', `Bearer ${userToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.application.status).toBe('completed');
    expect(response.body.application.actualSampleCount).toBe(testReportCompletion.actualSampleCount);
    expect(response.body.application.actualProteinCount).toBe(testReportCompletion.actualProteinCount);
    expect(response.body.application.previewDataFile).toBeDefined();
    expect(response.body.application.completeDataFile).toBeDefined();
    expect(response.body.application.previewReportFile).toBeDefined();
    expect(response.body.application.fullReportFile).toBeDefined();
  });

  test('6. Verify Data File Information', async () => {
    const response = await request(API_BASE_URL)
      .get(`/api/applications/${applicationId}`)
      .set('Authorization', `Bearer ${userToken}`);
    
    const app = response.body.application;
    
    // Verify preview data file
    expect(app.previewDataFile.originalName).toBe('preview-data.csv');
    expect(app.previewDataFile.type).toBe('preview_data');
    expect(app.previewDataFile.size).toBeGreaterThan(0);
    
    // Verify complete data file
    expect(app.completeDataFile.originalName).toBe('complete-data.csv');
    expect(app.completeDataFile.type).toBe('complete_data');
    expect(app.completeDataFile.size).toBeGreaterThan(0);
  });

  test('7. Admin Views Application List with New Fields', async () => {
    const response = await request(API_BASE_URL)
      .get('/api/admin/applications')
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    const application = response.body.applications.find(app => app.id === applicationId);
    expect(application).toBeDefined();
    expect(application.actualSampleCount).toBe(testReportCompletion.actualSampleCount);
    expect(application.actualProteinCount).toBe(testReportCompletion.actualProteinCount);
  });
});

// Run the test
if (require.main === module) {
  console.log('🧪 Starting End-to-End Report Completion Test...');
  console.log('📋 Test will validate:');
  console.log('   ✓ User application submission');
  console.log('   ✓ Admin report completion with new fields');
  console.log('   ✓ User viewing completed report');
  console.log('   ✓ Data file handling and display');
  console.log('   ✓ Actual sample/protein count tracking');
  console.log('');
  console.log('🚀 Run with: npm test -- tests/e2e-report-completion-test.js');
}

module.exports = {
  testUser,
  testAdmin,
  testApplication,
  testReportCompletion
};

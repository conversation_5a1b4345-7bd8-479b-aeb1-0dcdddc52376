<template>
  <div class="admin-dashboard">
    <div class="admin-header">
      <div class="header-left">
        <h1>{{ $t('adminDashboard') }}</h1>
        <router-link to="/" class="homepage-link">
          <span class="homepage-icon">🏠</span>
          {{ $t('backToHomepage') }}
        </router-link>
      </div>
      <div class="admin-user-info">
        <div class="language-switcher">
          <select v-model="currentLocale" @change="changeLanguage" class="language-select">
            <option value="en">English</option>
            <option value="zh">中文</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
            <option value="de">Deutsch</option>
            <option value="ja">日本語</option>
            <option value="ko">한국어</option>
          </select>
        </div>
        <span>{{ $t('welcome') }}, {{ userStore.user?.firstName || 'Admin' }}</span>
        <button @click="logout" class="logout-btn">{{ $t('logout') }}</button>
      </div>
    </div>

    <div class="admin-content">
      <!-- 统计卡片 -->
      <StatsCards :stats="adminStats.stats" />

      <!-- 申请管理表格 -->
      <ApplicationsTable
        :applications="adminApplications.applications.value"
        :loading="adminApplications.loading.value"
        :pagination="adminApplications.pagination"
        :filters="adminApplications.filters"
        @apply-filters="adminApplications.applyFilters"
        @reset-filters="adminApplications.resetFilters"
        @view-application="adminApplications.openModal"
        @update-status="adminApplications.updateApplicationStatus"
        @go-to-page="adminApplications.goToPage"
      />

      <!-- 申请详情模态框 -->
      <ApplicationModal
        :show="adminApplications.showModal.value"
        :application="adminApplications.selectedApplication.value"
        :loading="completionLoading"
        @close="adminApplications.closeModal"
        @complete="handleCompleteApplication"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/counter'
import { adminAPI } from '@/services/api'

// 导入组件
import StatsCards from './components/StatsCards.vue'
import ApplicationsTable from './components/ApplicationsTable.vue'
import ApplicationModal from './components/ApplicationModal.vue'

// 导入组合式函数
import { useAdminStats } from './composables/useAdminStats'
import { useAdminApplications } from './composables/useAdminApplications'

const router = useRouter()
const { locale } = useI18n()
const userStore = useUserStore()

// 使用组合式函数
const adminStats = useAdminStats()
const adminApplications = useAdminApplications()

// 本地状态
const completionLoading = ref(false)
const currentLocale = computed({
  get: () => locale.value,
  set: (value) => {
    locale.value = value
    localStorage.setItem('locale', value)
  }
})

// 方法
const changeLanguage = () => {
  // 语言已通过计算属性自动更新
}

const logout = async () => {
  try {
    await userStore.logout()
    router.push('/admin/login')
  } catch (error) {
    console.error('Logout error:', error)
  }
}

const handleCompleteApplication = async (data: any) => {
  try {
    completionLoading.value = true
    
    const { completionForm, files } = data
    
    // 获取文件
    const previewFile = files.previewReportFileRef?.files?.[0]
    const fullFile = files.fullReportFileRef?.files?.[0]
    const previewDataFile = files.previewDataFileRef?.files?.[0]
    const completeDataFile = files.completeDataFileRef?.files?.[0]

    // 验证必需文件
    const missingFields = []
    if (!previewFile) missingFields.push('预览报告文件')
    if (!fullFile) missingFields.push('完整报告文件')
    if (!completeDataFile) missingFields.push('完整数据文件（必需）')
    if (!completionForm.actualSampleCount || completionForm.actualSampleCount <= 0) {
      missingFields.push('实际样本数量（必须大于0）')
    }
    if (!completionForm.reportPrice || completionForm.reportPrice <= 0) {
      missingFields.push('报告价格（必须大于0）')
    }

    if (missingFields.length > 0) {
      alert(`请完善以下必填字段：\n${missingFields.join('\n')}`)
      return
    }

    // 构建FormData
    const formData = new FormData()
    formData.append('previewReport', previewFile!)
    formData.append('fullReport', fullFile!)
    formData.append('completeDataFile', completeDataFile!)
    if (previewDataFile) {
      formData.append('previewDataFile', previewDataFile)
    }
    formData.append('actualSampleCount', completionForm.actualSampleCount.toString())
    if (completionForm.actualProteinCount) {
      formData.append('actualProteinCount', completionForm.actualProteinCount.toString())
    }
    formData.append('reportPrice', completionForm.reportPrice.toString())
    formData.append('paymentStatus', completionForm.paymentStatus)
    formData.append('notes', completionForm.notes)

    // 提交申请完成
    if (!adminApplications.selectedApplication.value) {
      throw new Error('No application selected')
    }
    
    await adminAPI.completeApplication(adminApplications.selectedApplication.value!.id, formData)
    
    alert('申请完成成功！报告和数据文件已上传')
    
    // 关闭模态框并刷新数据
    adminApplications.closeModal()
    await adminApplications.fetchApplications()
    await adminStats.fetchStats()
    
  } catch (error: any) {
    console.error('Complete application error:', error)
    
    let errorMessage = '完成申请时发生错误：'
    if (error.response?.data?.details && error.response.data.details.length > 0) {
      const fieldErrors = error.response.data.details.map((detail: any) => {
        const fieldName = detail.path || detail.param
        const message = detail.msg
        return `${fieldName}: ${message}`
      })
      errorMessage += '\n' + fieldErrors.join('\n')
    } else if (error.response?.data?.message) {
      errorMessage += '\n' + error.response.data.message
    } else {
      errorMessage += '\n' + error.message
    }
    
    alert(errorMessage)
  } finally {
    completionLoading.value = false
  }
}

// 生命周期
onMounted(async () => {
  try {
    await Promise.all([
      adminStats.fetchStats(),
      adminApplications.fetchApplications()
    ])
  } catch (error) {
    console.error('Failed to load admin dashboard data:', error)
  }
})
</script>

<style scoped>
.admin-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.admin-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left h1 {
  margin: 0;
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #667eea;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.homepage-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #5a67d8;
}

.homepage-icon {
  font-size: 1.2rem;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.language-switcher select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.logout-btn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: #c53030;
  transform: translateY(-1px);
}

.admin-content {
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

@media (max-width: 768px) {
  .admin-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }
  
  .header-left {
    flex-direction: column;
    gap: 10px;
  }
  
  .admin-user-info {
    flex-direction: column;
    gap: 10px;
  }
  
  .admin-content {
    padding: 20px;
  }
}
</style>

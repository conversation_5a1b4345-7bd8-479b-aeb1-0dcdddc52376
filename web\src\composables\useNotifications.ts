import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  timestamp: number
}

interface NotificationOptions {
  title: string
  message?: string
  duration?: number
  persistent?: boolean
}

export function useNotifications() {
  const { t } = useI18n()

  // 通知列表
  const notifications = ref<Notification[]>([])

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 添加通知
  const addNotification = (type: Notification['type'], options: NotificationOptions) => {
    const notification: Notification = {
      id: generateId(),
      type,
      title: options.title,
      message: options.message,
      duration: options.duration || (type === 'error' ? 5000 : 3000),
      persistent: options.persistent || false,
      timestamp: Date.now()
    }

    notifications.value.push(notification)

    // 自动移除非持久化通知
    if (!notification.persistent && (notification.duration ?? 0) > 0) {
      setTimeout(() => {
        removeNotification(notification.id)
      }, notification.duration ?? 0)
    }

    return notification.id
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清空所有通知
  const clearNotifications = () => {
    notifications.value = []
  }

  // 成功通知
  const success = (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return addNotification('success', {
      title,
      message,
      ...options
    })
  }

  // 错误通知
  const error = (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return addNotification('error', {
      title,
      message,
      duration: 5000,
      ...options
    })
  }

  // 警告通知
  const warning = (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return addNotification('warning', {
      title,
      message,
      ...options
    })
  }

  // 信息通知
  const info = (title: string, message?: string, options?: Partial<NotificationOptions>) => {
    return addNotification('info', {
      title,
      message,
      ...options
    })
  }

  // API错误处理
  const handleApiError = (err: any, defaultMessage?: string) => {
    console.error('API Error:', err)

    let title = t('error')
    let message = defaultMessage || t('unknownError')

    if (err.response) {
      // 服务器响应错误
      const status = err.response.status
      const data = err.response.data

      if (status === 401) {
        title = t('authenticationError')
        message = t('pleaseLoginAgain')
      } else if (status === 403) {
        title = t('permissionError')
        message = t('noPermissionToPerformAction')
      } else if (status === 404) {
        title = t('notFoundError')
        message = t('requestedResourceNotFound')
      } else if (status === 422) {
        title = t('validationError')
        if (data.details && data.details.length > 0) {
          message = data.details.map((detail: any) => detail.msg).join('; ')
        } else {
          message = data.message || t('validationFailed')
        }
      } else if (status >= 500) {
        title = t('serverError')
        message = t('serverErrorMessage')
      } else {
        message = data.message || message
      }
    } else if (err.request) {
      // 网络错误
      title = t('networkError')
      message = t('networkErrorMessage')
    } else {
      // 其他错误
      message = err.message || message
    }

    return error(title, message)
  }

  // 成功操作通知
  const handleSuccess = (operation: string, details?: string) => {
    const title = t('operationSuccessful')
    const message = details || t('operationCompletedSuccessfully', { operation })
    return success(title, message)
  }

  // 加载状态通知
  const showLoading = (message: string = t('loading')) => {
    return info(message, undefined, { persistent: true })
  }

  // 隐藏加载通知
  const hideLoading = (id: string) => {
    removeNotification(id)
  }

  // 确认操作
  const confirm = (title: string, message?: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const confirmed = window.confirm(`${title}\n${message || ''}`)
      resolve(confirmed)
    })
  }

  // 获取通知统计
  const getNotificationStats = () => {
    const stats = {
      total: notifications.value.length,
      success: 0,
      error: 0,
      warning: 0,
      info: 0
    }

    notifications.value.forEach(notification => {
      stats[notification.type]++
    })

    return stats
  }

  // 获取最新通知
  const getLatestNotifications = (count: number = 5) => {
    return notifications.value
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, count)
  }

  return {
    // 状态
    notifications,

    // 基础方法
    addNotification,
    removeNotification,
    clearNotifications,

    // 快捷方法
    success,
    error,
    warning,
    info,

    // 高级方法
    handleApiError,
    handleSuccess,
    showLoading,
    hideLoading,
    confirm,

    // 工具方法
    getNotificationStats,
    getLatestNotifications
  }
}

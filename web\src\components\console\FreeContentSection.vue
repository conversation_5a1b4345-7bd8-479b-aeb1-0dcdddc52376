<template>
  <div class="free-content-section">
    <h4 class="section-title">
      🆓 {{ $t('freeContent') }}
    </h4>

    <!-- 免费内容网格 -->
    <div class="free-content-grid">
      <!-- 预览报告 -->
      <BaseCard
        v-if="previewReport"
        variant="default"
        padding="lg"
        hoverable
        class="content-card preview-report"
      >
        <template #header>
          <div class="content-header">
            <h5 class="content-title">📄 {{ $t('previewReport') }}</h5>
            <StatusBadge status="free" size="md">{{ $t('free') }}</StatusBadge>
          </div>
        </template>

        <div class="content-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ previewReport.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(previewReport.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('generationTime') }}:</span>
            <span class="detail-value">{{ formatDate(previewReport.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            variant="success"
            size="lg"
            @click="downloadReport('preview')"
            :loading="downloading === 'preview'"
          >
            📥 {{ $t('downloadPreviewReport') }}
          </BaseButton>
        </template>
      </BaseCard>

      <!-- 预览数据文件 -->
      <BaseCard
        v-if="previewDataFile"
        variant="default"
        padding="lg"
        hoverable
        class="content-card preview-data"
      >
        <template #header>
          <div class="content-header">
            <h5 class="content-title">👁️ {{ $t('previewDataFile') }}</h5>
            <StatusBadge status="free" size="md">{{ $t('free') }}</StatusBadge>
          </div>
        </template>

        <div class="content-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ previewDataFile.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(previewDataFile.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('uploadTime') }}:</span>
            <span class="detail-value">{{ formatDate(previewDataFile.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            variant="outline"
            size="lg"
            @click="downloadDataFile('preview')"
            :loading="downloading === 'preview-data'"
          >
            📥 {{ $t('downloadPreviewData') }}
          </BaseButton>
        </template>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, StatusBadge } from '@/components/ui'

interface FileInfo {
  originalName: string
  size: number
  uploadedAt: string
}

interface Props {
  previewReport?: FileInfo | null
  previewDataFile?: FileInfo | null
  applicationId: number
}

interface Emits {
  (e: 'download-report', type: 'preview'): void
  (e: 'download-data-file', type: 'preview'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

const downloading = ref<string | null>(null)

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// 下载报告
const downloadReport = async (type: 'preview') => {
  downloading.value = type
  try {
    emit('download-report', type)
  } finally {
    downloading.value = null
  }
}

// 下载数据文件
const downloadDataFile = async (type: 'preview') => {
  downloading.value = 'preview-data'
  try {
    emit('download-data-file', type)
  } finally {
    downloading.value = null
  }
}
</script>

<style scoped>
.free-content-section {
  margin-bottom: 2rem;
}

.section-title {
  color: #2d3748;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 免费内容网格布局 */
.free-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
}

.content-card {
  height: fit-content;
  border: 2px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.content-title {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.content-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 700;
  color: #4a5568;
  font-size: 1rem;
}

.detail-value {
  color: #2d3748;
  font-size: 1rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .free-content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .section-title {
    font-size: 1.25rem;
  }
  
  .content-title {
    font-size: 1.125rem;
  }
}
</style>

import axios from "axios";
import {
  mockAuthAPI,
  mockApplicationAPI,
  mockUploadAPI,
  mockAdminAPI,
  mockPaymentAPI
} from "./mockApi.js";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || "/api",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem("authToken");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (data) => api.post("/auth/register", data),
  verifyEmail: (data) => api.post("/auth/verify-email", data),
  resendVerification: (data) => api.post("/auth/resend-verification", data),
  login: (data) => api.post("/auth/login", data),
  logout: () => api.post("/auth/logout"),
  getCurrentUser: () => api.get("/auth/me"),
};

// User API
export const userAPI = {
  updateProfile: (data) => api.patch("/users/profile", data),
  updatePreferences: (data) => api.patch("/users/preferences", data),
  changePassword: (data) => api.patch("/users/password", data),
  getStats: () => api.get("/users/stats"),
  deactivateAccount: (data) => api.post("/users/deactivate", data),
  deleteAccount: (data) => api.delete("/users/account", { data }),
};

// File Upload API
export const uploadAPI = {
  uploadFiles: (formData) =>
    api.post("/upload/files", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  getFiles: () => api.get("/upload/files"),
  getFileInfo: (filename) => api.get(`/upload/files/${filename}`),
  downloadFile: (filename) =>
    api.get(`/upload/files/${filename}/download`, {
      responseType: "blob",
    }),
  deleteFile: (filename) => api.delete(`/upload/files/${filename}`),
  validateFile: (filename) => api.post(`/upload/files/${filename}/validate`),
};

// Application API
export const applicationAPI = {
  submitApplication: (data) => api.post("/applications", data),
  getApplications: (params) => api.get("/applications", { params }),
  getApplication: (id) => api.get(`/applications/${id}`),
  updateApplication: (id, data) => api.patch(`/applications/${id}`, data),
  cancelApplication: (id) => api.post(`/applications/${id}/cancel`),
  deleteApplication: (id) => api.delete(`/applications/${id}`),
  getApplicationStats: () => api.get("/applications/stats/summary"),
  downloadReport: (id, reportType) =>
    api.get(`/applications/${id}/download/${reportType}`, {
      responseType: "blob",
    }),
};

// Report API
export const reportAPI = {
  getReportByApplication: (applicationId) =>
    api.get(`/reports/application/${applicationId}`),
  getReport: (reportId) => api.get(`/reports/${reportId}`),
  downloadReport: (reportId, format = "pdf") =>
    api.get(`/reports/${reportId}/download`, {
      params: { format },
      responseType: "blob",
    }),
  getReports: (params) => api.get("/reports", { params }),
};

// Chat API
export const chatAPI = {
  sendMessage: (data) => api.post("/chat/message", data),
  sendPublicMessage: (data) => api.post("/chat/public/message", data),
  getSuggestions: (language) =>
    api.get("/chat/suggestions", { params: { language } }),
  getChatHistory: (params) => api.get("/chat/history", { params }),
  clearChatHistory: () => api.delete("/chat/history"),
  getChatStats: () => api.get("/chat/stats"),
  getChatHealth: () => api.get("/chat/health"),
};

// Health API
export const healthAPI = {
  getHealth: () => api.get("/health"),
};

// Payment API
export const paymentAPI = {
  createPayment: (data) => api.post("/payments/create", data),
  getPaymentStatus: (paymentId) => api.get(`/payments/${paymentId}/status`),
  getQRCode: (paymentId) => api.get(`/payments/${paymentId}/qrcode`),
  cancelPayment: (paymentId) => api.post(`/payments/${paymentId}/cancel`),
  simulatePayment: (paymentId) =>
    api.post(`/payments/${paymentId}/simulate-success`),
  getPaymentHistory: (params) => api.get("/payments/history", { params }),
  checkSimulateEnabled: () => api.get("/payments/simulate-enabled"),
};

// System API
export const systemAPI = {
  checkInitStatus: () => api.get("/system/init-status"),
  initialize: (data) => api.post("/system/initialize", data),
};

// Admin API
export const adminAPI = {
  // Dashboard
  getDashboardStats: () => api.get("/admin/dashboard/stats"),
  getSystemHealth: () => api.get("/admin/system/health"),

  // User Management
  getUsers: (params) => api.get("/admin/users", { params }),
  createUser: (data) => api.post("/admin/users", data),
  updateUser: (id, data) => api.patch(`/admin/users/${id}`, data),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  toggleUserStatus: (id, action) => api.patch(`/admin/users/${id}/${action}`),
  resetUserPassword: (id, data) =>
    api.patch(`/admin/users/${id}/reset-password`, data),
  promoteUser: (id, data) => api.patch(`/admin/users/${id}/role`, data),

  // Application Management
  getApplications: (params) => api.get("/admin/applications", { params }),
  getApplication: (id) => api.get(`/admin/applications/${id}`),
  updateApplicationStatus: (id, data) =>
    api.patch(`/admin/applications/${id}/status`, data),
  deleteApplication: (id) => api.delete(`/admin/applications/${id}`),
  completeApplication: (id, formData) =>
    api.post(`/admin/applications/${id}/complete`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  uploadResultFiles: (id, formData) =>
    api.post(`/admin/applications/${id}/results`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  downloadReport: (id, reportType) =>
    api.get(`/admin/applications/${id}/download/${reportType}`, {
      responseType: "blob",
    }),
};

// Utility functions
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem("authToken", token);
    api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    localStorage.removeItem("authToken");
    delete api.defaults.headers.common["Authorization"];
  }
};

export const getAuthToken = () => {
  return localStorage.getItem("authToken");
};

export const isAuthenticated = () => {
  return !!getAuthToken();
};

// Error handling utility
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      status,
      message: data.message || data.error || "An error occurred",
      details: data.details || null,
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      status: 0,
      message: "Network error. Please check your connection.",
      details: null,
    };
  } else {
    // Something else happened
    return {
      status: 0,
      message: error.message || "An unexpected error occurred",
      details: null,
    };
  }
};

// Check if we should use mock API
const USE_MOCK_API = import.meta.env.VITE_USE_MOCK_API === 'true';

// Override API exports with mock versions if enabled
if (USE_MOCK_API) {
  console.log('🔧 Using Mock API');

  // Override auth API
  Object.assign(authAPI, mockAuthAPI);

  // Override application API
  Object.assign(applicationAPI, mockApplicationAPI);

  // Override upload API
  Object.assign(uploadAPI, mockUploadAPI);

  // Override admin API
  Object.assign(adminAPI, mockAdminAPI);

  // Override payment API
  Object.assign(paymentAPI, mockPaymentAPI);
} else {
  console.log('🔧 Using Real API');
}

export default api;

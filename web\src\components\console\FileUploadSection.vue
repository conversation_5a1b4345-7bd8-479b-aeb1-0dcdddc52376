<template>
  <BaseCard
    variant="outlined"
    padding="lg"
    class="file-upload-section"
  >
    <template #header>
      <h3 class="section-title">
        📁 {{ $t('fileUpload') }}
      </h3>
    </template>

    <div class="upload-areas">
      <!-- 蛋白组数据文件（必选） -->
      <div class="upload-area">
        <h4 class="upload-title required">
          {{ $t('proteinDataFileRequired') }}
        </h4>
        
        <div class="upload-zone" :class="{ 'drag-over': proteinDragOver }">
          <input
            ref="proteinFileInput"
            type="file"
            multiple
            accept=".xlsx,.xls,.csv,.txt"
            :disabled="uploading"
            @change="handleProteinFileUpload"
            class="file-input"
          />
          
          <div 
            class="upload-content"
            @click="triggerProteinFileInput"
            @dragover.prevent="proteinDragOver = true"
            @dragleave.prevent="proteinDragOver = false"
            @drop.prevent="handleProteinFileDrop"
          >
            <div class="upload-icon">📊</div>
            <div class="upload-text">
              <p class="upload-primary">{{ $t('clickToUploadProteinData') }}</p>
              <p class="upload-secondary">{{ $t('dragDropSupported') }}</p>
              <p class="upload-hint">{{ $t('proteinFileFormats') }}</p>
            </div>
          </div>
        </div>

        <!-- 已上传的蛋白组文件 -->
        <div v-if="proteinFiles.length > 0" class="uploaded-files">
          <h5 class="files-title">{{ $t('uploadedProteinFiles') }}</h5>
          <div class="files-list">
            <div
              v-for="file in proteinFiles"
              :key="file.filename"
              class="file-item"
            >
              <div class="file-info">
                <span class="file-name">{{ file.originalName }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <BaseButton
                variant="danger"
                size="sm"
                @click="removeProteinFile(file.filename)"
                :disabled="uploading"
              >
                {{ $t('remove') }}
              </BaseButton>
            </div>
          </div>
        </div>

        <div v-if="errors.proteinFiles" class="error-message">
          {{ errors.proteinFiles }}
        </div>
      </div>

      <!-- 样本分组数据文件（可选） -->
      <div class="upload-area">
        <h4 class="upload-title">
          {{ $t('sampleGroupFileOptional') }}
        </h4>
        
        <div class="upload-zone" :class="{ 'drag-over': groupDragOver }">
          <input
            ref="groupFileInput"
            type="file"
            multiple
            accept=".xlsx,.xls,.csv,.txt"
            :disabled="uploading"
            @change="handleGroupFileUpload"
            class="file-input"
          />
          
          <div 
            class="upload-content"
            @click="triggerGroupFileInput"
            @dragover.prevent="groupDragOver = true"
            @dragleave.prevent="groupDragOver = false"
            @drop.prevent="handleGroupFileDrop"
          >
            <div class="upload-icon">📋</div>
            <div class="upload-text">
              <p class="upload-primary">{{ $t('clickToUploadGroupData') }}</p>
              <p class="upload-secondary">{{ $t('dragDropSupported') }}</p>
              <p class="upload-hint">{{ $t('groupFileFormats') }}</p>
            </div>
          </div>
        </div>

        <!-- 已上传的分组文件 -->
        <div v-if="groupFiles.length > 0" class="uploaded-files">
          <h5 class="files-title">{{ $t('uploadedGroupFiles') }}</h5>
          <div class="files-list">
            <div
              v-for="file in groupFiles"
              :key="file.filename"
              class="file-item"
            >
              <div class="file-info">
                <span class="file-name">{{ file.originalName }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <BaseButton
                variant="danger"
                size="sm"
                @click="removeGroupFile(file.filename)"
                :disabled="uploading"
              >
                {{ $t('remove') }}
              </BaseButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <LoadingState
        type="spinner"
        size="sm"
        :text="$t('uploadingFiles')"
      />
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, LoadingState } from '@/components/ui'

interface UploadedFile {
  filename: string
  originalName: string
  size: number
}

interface FileUploadData {
  proteinFiles: UploadedFile[]
  groupFiles: UploadedFile[]
}

interface Props {
  modelValue: FileUploadData
  uploading?: boolean
  errors?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  uploading: false,
  errors: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: FileUploadData]
  uploadFiles: [files: FileList, type: 'protein' | 'group']
  removeFile: [filename: string, type: 'protein' | 'group']
}>()

const { t } = useI18n()

const proteinFileInput = ref<HTMLInputElement>()
const groupFileInput = ref<HTMLInputElement>()
const proteinDragOver = ref(false)
const groupDragOver = ref(false)

const proteinFiles = ref<UploadedFile[]>(props.modelValue.proteinFiles || [])
const groupFiles = ref<UploadedFile[]>(props.modelValue.groupFiles || [])

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  proteinFiles.value = newValue.proteinFiles || []
  groupFiles.value = newValue.groupFiles || []
}, { deep: true })

const triggerProteinFileInput = () => {
  proteinFileInput.value?.click()
}

const triggerGroupFileInput = () => {
  groupFileInput.value?.click()
}

const handleProteinFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    emit('uploadFiles', target.files, 'protein')
  }
}

const handleGroupFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    emit('uploadFiles', target.files, 'group')
  }
}

const handleProteinFileDrop = (event: DragEvent) => {
  proteinDragOver.value = false
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    emit('uploadFiles', event.dataTransfer.files, 'protein')
  }
}

const handleGroupFileDrop = (event: DragEvent) => {
  groupDragOver.value = false
  if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
    emit('uploadFiles', event.dataTransfer.files, 'group')
  }
}

const removeProteinFile = (filename: string) => {
  emit('removeFile', filename, 'protein')
}

const removeGroupFile = (filename: string) => {
  emit('removeFile', filename, 'group')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-upload-section {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-areas {
  display: grid;
  gap: 2rem;
}

.upload-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-title {
  margin: 0;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-title.required::after {
  content: '*';
  color: #ef4444;
  margin-left: 0.25rem;
}

.upload-zone {
  position: relative;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.upload-zone:hover,
.upload-zone.drag-over {
  border-color: #667eea;
  background: #f0f4ff;
}

.file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.upload-content {
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.upload-primary {
  margin: 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1rem;
}

.upload-secondary {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.upload-hint {
  margin: 0;
  color: #9ca3af;
  font-size: 0.75rem;
}

.uploaded-files {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.files-title {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 600;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.file-name {
  color: #2d3748;
  font-weight: 500;
  font-size: 0.875rem;
}

.file-size {
  color: #6b7280;
  font-size: 0.75rem;
}

.upload-progress {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f4ff;
  border: 1px solid #c7d2fe;
  border-radius: 8px;
  text-align: center;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

/* 响应式设计 */
@media (min-width: 1024px) {
  .upload-areas {
    grid-template-columns: 1fr 1fr;
  }
}
</style>

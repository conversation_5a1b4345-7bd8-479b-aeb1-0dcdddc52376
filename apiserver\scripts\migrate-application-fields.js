const { sequelize } = require('../models');
require('dotenv').config();

/**
 * 数据库迁移脚本：为Application表添加新的字段
 * 
 * 此脚本将为现有的Application表添加以下新字段：
 * - proteinPanel (必填)
 * - proteinPanelOption (可选)
 * - otherPlatformName (可选)
 * - sampleCount (必填)
 * - projectName (可选)
 * - sampleOrigin (可选)
 * - contactName (可选)
 * - contactOrganization (可选)
 * - sampleType (可选)
 * - otherSampleType (可选)
 * - contractNumber (可选)
 * - proteinDataFiles (JSON字段)
 * - sampleGroupFiles (JSON字段)
 */

async function migrateApplicationFields() {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    console.log('🔄 开始数据库迁移：添加Application表新字段...');
    
    // 检查数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 获取当前表结构
    const tableDescription = await queryInterface.describeTable('applications');
    console.log('📋 当前表结构已获取');
    
    const fieldsToAdd = [];
    
    // 检查并添加proteinPanel字段
    if (!tableDescription.proteinPanel) {
      fieldsToAdd.push({
        name: 'proteinPanel',
        definition: {
          type: sequelize.Sequelize.ENUM('olink', 'somalogic', 'other'),
          allowNull: true, // 先设为可空，稍后更新现有记录后改为不可空
          comment: '蛋白组Panel类型'
        }
      });
    }
    
    // 检查并添加proteinPanelOption字段
    if (!tableDescription.proteinPanelOption) {
      fieldsToAdd.push({
        name: 'proteinPanelOption',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          allowNull: true,
          comment: '蛋白组Panel的具体选项'
        }
      });
    }
    
    // 检查并添加otherPlatformName字段
    if (!tableDescription.otherPlatformName) {
      fieldsToAdd.push({
        name: 'otherPlatformName',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          allowNull: true,
          comment: '其他平台名称'
        }
      });
    }
    
    // 检查并添加sampleCount字段
    if (!tableDescription.sampleCount) {
      fieldsToAdd.push({
        name: 'sampleCount',
        definition: {
          type: sequelize.Sequelize.INTEGER,
          allowNull: true, // 先设为可空，稍后更新现有记录后改为不可空
          comment: '样本数量'
        }
      });
    }
    
    // 检查并添加projectName字段
    if (!tableDescription.projectName) {
      fieldsToAdd.push({
        name: 'projectName',
        definition: {
          type: sequelize.Sequelize.STRING(200),
          allowNull: true,
          comment: '项目名称'
        }
      });
    }
    
    // 检查并添加sampleOrigin字段
    if (!tableDescription.sampleOrigin) {
      fieldsToAdd.push({
        name: 'sampleOrigin',
        definition: {
          type: sequelize.Sequelize.STRING(200),
          allowNull: true,
          comment: '样本来源'
        }
      });
    }
    
    // 检查并添加contactName字段
    if (!tableDescription.contactName) {
      fieldsToAdd.push({
        name: 'contactName',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          allowNull: true,
          comment: '联系人姓名'
        }
      });
    }
    
    // 检查并添加contactOrganization字段
    if (!tableDescription.contactOrganization) {
      fieldsToAdd.push({
        name: 'contactOrganization',
        definition: {
          type: sequelize.Sequelize.STRING(200),
          allowNull: true,
          comment: '联系机构'
        }
      });
    }
    
    // 检查并添加sampleType字段
    if (!tableDescription.sampleType) {
      fieldsToAdd.push({
        name: 'sampleType',
        definition: {
          type: sequelize.Sequelize.ENUM('human', 'mouse', 'monkey', 'pig', 'other'),
          allowNull: true,
          comment: '样本类型'
        }
      });
    }
    
    // 检查并添加otherSampleType字段
    if (!tableDescription.otherSampleType) {
      fieldsToAdd.push({
        name: 'otherSampleType',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          allowNull: true,
          comment: '其他样本类型'
        }
      });
    }
    
    // 检查并添加contractNumber字段
    if (!tableDescription.contractNumber) {
      fieldsToAdd.push({
        name: 'contractNumber',
        definition: {
          type: sequelize.Sequelize.STRING(100),
          allowNull: true,
          comment: '合同编号'
        }
      });
    }
    
    // 检查并添加proteinDataFiles字段
    if (!tableDescription.proteinDataFiles) {
      fieldsToAdd.push({
        name: 'proteinDataFiles',
        definition: {
          type: sequelize.Sequelize.JSON,
          allowNull: true,
          defaultValue: [],
          comment: '蛋白组数据文件'
        }
      });
    }
    
    // 检查并添加sampleGroupFiles字段
    if (!tableDescription.sampleGroupFiles) {
      fieldsToAdd.push({
        name: 'sampleGroupFiles',
        definition: {
          type: sequelize.Sequelize.JSON,
          allowNull: true,
          defaultValue: [],
          comment: '样本分组文件'
        }
      });
    }
    
    if (fieldsToAdd.length === 0) {
      console.log('ℹ️  所有字段已存在，无需添加新字段');
      return;
    }
    
    console.log(`📝 需要添加 ${fieldsToAdd.length} 个新字段:`);
    fieldsToAdd.forEach(field => {
      console.log(`   - ${field.name}`);
    });
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      // 添加新字段
      for (const field of fieldsToAdd) {
        console.log(`🔧 添加字段: ${field.name}`);
        await queryInterface.addColumn('applications', field.name, field.definition, { transaction });
        console.log(`   ✅ ${field.name} 字段添加成功`);
      }
      
      // 为现有记录设置默认值
      console.log('🔄 为现有记录设置默认值...');
      
      // 获取现有记录数量
      const [results] = await sequelize.query('SELECT COUNT(*) as count FROM applications', { transaction });
      const existingRecordsCount = results[0].count;
      
      if (existingRecordsCount > 0) {
        console.log(`📊 发现 ${existingRecordsCount} 条现有记录，正在设置默认值...`);
        
        // 为现有记录设置默认值
        await sequelize.query(`
          UPDATE applications 
          SET 
            proteinPanel = 'olink',
            sampleCount = 1,
            proteinDataFiles = '[]',
            sampleGroupFiles = '[]'
          WHERE proteinPanel IS NULL
        `, { transaction });
        
        console.log('   ✅ 现有记录默认值设置完成');
      }
      
      // 提交事务
      await transaction.commit();
      console.log('✅ 数据库迁移完成！');
      
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    console.error('详细错误信息:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateApplicationFields()
    .then(() => {
      console.log('🎉 迁移脚本执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateApplicationFields };

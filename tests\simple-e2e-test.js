/**
 * Simple End-to-End Test for Report Completion Feature
 * 
 * This test validates the complete workflow from application submission
 * to report completion with new fields (preview data file, complete data file,
 * actual sample count, actual protein count).
 */

const request = require('supertest');
const fs = require('fs');
const path = require('path');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const TEST_FILES_DIR = path.join(__dirname, 'test-files');

// Test data
const testUser = {
  email: `test-user-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  confirmPassword: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User'
};

const testAdmin = {
  email: '<EMAIL>',
  password: 'AdminPassword123!'
};

const testApplication = {
  analysisType: 'protein_complex',
  proteinPanel: 'olink_explore_1536',
  sampleQuantity: 50,
  projectName: 'E2E Test Project',
  sampleSource: 'Human Plasma',
  contactName: 'Test Contact',
  contactEmail: '<EMAIL>',
  contactPhone: '+1234567890',
  sampleType: 'plasma',
  contractNumber: 'CONTRACT-2024-001'
};

const testReportData = {
  reportPrice: 299.99,
  paymentStatus: 'unpaid',
  actualSampleCount: 48,
  actualProteinCount: 1520,
  notes: 'E2E test completion'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`${colors.cyan}ℹ️  ${message}${colors.reset}`);
}

// Helper functions
function createTestFile(filename, content) {
  if (!fs.existsSync(TEST_FILES_DIR)) {
    fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
  }
  const filePath = path.join(TEST_FILES_DIR, filename);
  fs.writeFileSync(filePath, content);
  return filePath;
}

function cleanupTestFiles() {
  if (fs.existsSync(TEST_FILES_DIR)) {
    fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
  }
}

// Test functions
async function testUserRegistrationAndLogin() {
  logInfo('Testing user registration and login...');
  
  try {
    // Register test user
    const registerResponse = await request(API_BASE_URL)
      .post('/api/auth/register')
      .send(testUser);
    
    if (registerResponse.status !== 201) {
      throw new Error(`Registration failed: ${registerResponse.status} - ${registerResponse.text}`);
    }

    // Login test user
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });
    
    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${loginResponse.status} - ${loginResponse.text}`);
    }

    const userToken = loginResponse.body.token;
    logSuccess('User registration and login completed');
    return userToken;
    
  } catch (error) {
    logError(`User registration/login failed: ${error.message}`);
    throw error;
  }
}

async function testApplicationSubmission(userToken) {
  logInfo('Testing application submission...');
  
  try {
    const response = await request(API_BASE_URL)
      .post('/api/applications')
      .set('Authorization', `Bearer ${userToken}`)
      .send(testApplication);
    
    if (response.status !== 201) {
      throw new Error(`Application submission failed: ${response.status} - ${response.text}`);
    }

    const applicationId = response.body.id;
    logSuccess(`Application submitted with ID: ${applicationId}`);
    return applicationId;
    
  } catch (error) {
    logError(`Application submission failed: ${error.message}`);
    throw error;
  }
}

async function testAdminLogin() {
  logInfo('Testing admin login...');
  
  try {
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send(testAdmin);
    
    if (loginResponse.status !== 200) {
      throw new Error(`Admin login failed: ${loginResponse.status} - ${loginResponse.text}`);
    }

    const adminToken = loginResponse.body.token;
    logSuccess('Admin login completed');
    return adminToken;
    
  } catch (error) {
    logError(`Admin login failed: ${error.message}`);
    throw error;
  }
}

async function testReportCompletion(adminToken, applicationId, previewDataFile, completeDataFile) {
  logInfo('Testing report completion with new fields...');
  
  try {
    const formData = new FormData();
    formData.append('reportPrice', testReportData.reportPrice.toString());
    formData.append('paymentStatus', testReportData.paymentStatus);
    formData.append('actualSampleCount', testReportData.actualSampleCount.toString());
    formData.append('actualProteinCount', testReportData.actualProteinCount.toString());
    formData.append('notes', testReportData.notes);
    
    // Add files
    if (fs.existsSync(previewDataFile)) {
      formData.append('previewDataFile', fs.createReadStream(previewDataFile));
    }
    if (fs.existsSync(completeDataFile)) {
      formData.append('completeDataFile', fs.createReadStream(completeDataFile));
    }

    const response = await request(API_BASE_URL)
      .put(`/api/admin/applications/${applicationId}/complete`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send(formData);
    
    if (response.status !== 200) {
      throw new Error(`Report completion failed: ${response.status} - ${response.text}`);
    }

    logSuccess('Report completion with new fields completed');
    return response.body;
    
  } catch (error) {
    logError(`Report completion failed: ${error.message}`);
    throw error;
  }
}

async function testUserViewCompletedReport(userToken, applicationId) {
  logInfo('Testing user view of completed report...');
  
  try {
    const response = await request(API_BASE_URL)
      .get(`/api/applications/${applicationId}`)
      .set('Authorization', `Bearer ${userToken}`);
    
    if (response.status !== 200) {
      throw new Error(`Failed to fetch completed report: ${response.status} - ${response.text}`);
    }

    const application = response.body;
    
    // Verify new fields are present
    if (!application.actualSampleCount) {
      throw new Error('actualSampleCount field is missing');
    }
    if (!application.actualProteinCount) {
      throw new Error('actualProteinCount field is missing');
    }
    
    logSuccess('User can view completed report with new fields');
    logInfo(`Actual Sample Count: ${application.actualSampleCount}`);
    logInfo(`Actual Protein Count: ${application.actualProteinCount}`);
    
    return application;
    
  } catch (error) {
    logError(`User view completed report failed: ${error.message}`);
    throw error;
  }
}

// Main test runner
async function runSimpleE2ETest() {
  log(`${colors.bright}🧪 Simple E2E Test - Report Completion Feature${colors.reset}`);
  log('');

  let userToken, adminToken, applicationId;
  let previewDataFile, completeDataFile;

  try {
    // Setup test files
    logInfo('Setting up test files...');
    previewDataFile = createTestFile('preview-data.csv', 'sample_id,protein_1,protein_2\nS001,100,200\nS002,150,250');
    completeDataFile = createTestFile('complete-data.csv', 'sample_id,protein_1,protein_2,protein_3\nS001,100,200,300\nS002,150,250,350');
    logSuccess('Test files created');

    // Run tests
    userToken = await testUserRegistrationAndLogin();
    applicationId = await testApplicationSubmission(userToken);
    adminToken = await testAdminLogin();
    await testReportCompletion(adminToken, applicationId, previewDataFile, completeDataFile);
    await testUserViewCompletedReport(userToken, applicationId);

    // Success
    log('');
    log(`${colors.green}${colors.bright}🎉 All E2E tests passed successfully!${colors.reset}`);
    log('');
    log('📊 Test Results:');
    log('   ✅ User registration and login');
    log('   ✅ Application submission');
    log('   ✅ Admin login');
    log('   ✅ Report completion with new fields');
    log('   ✅ User view of completed report');
    log('');

  } catch (error) {
    logError(`E2E test failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Cleanup
    logInfo('Cleaning up test files...');
    cleanupTestFiles();
    logSuccess('Cleanup completed');
  }
}

// Run the test
if (require.main === module) {
  runSimpleE2ETest().catch((error) => {
    logError(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runSimpleE2ETest
};

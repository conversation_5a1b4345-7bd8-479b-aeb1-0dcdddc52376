<template>
  <div class="actual-results-section">
    <h4 class="actual-results-section__title">
      🔬 {{ $t('actualResults') }}
    </h4>

    <div class="results-grid">
      <BaseCard
        v-if="actualSampleCount"
        variant="outlined"
        padding="md"
        class="result-card"
      >
        <div class="result-item">
          <div class="result-icon">🧪</div>
          <div class="result-content">
            <span class="result-label">{{ $t('actualSampleCount') }}</span>
            <span class="result-value">{{ actualSampleCount }}</span>
          </div>
        </div>
      </BaseCard>

      <BaseCard
        v-if="actualProteinCount"
        variant="outlined"
        padding="md"
        class="result-card"
      >
        <div class="result-item">
          <div class="result-icon">🧬</div>
          <div class="result-content">
            <span class="result-label">{{ $t('actualProteinCount') }}</span>
            <span class="result-value">{{ actualProteinCount }}</span>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 结果摘要 -->
    <BaseCard
      v-if="actualSampleCount && actualProteinCount"
      variant="flat"
      padding="md"
      class="results-summary"
    >
      <div class="summary-content">
        <div class="summary-icon">📊</div>
        <div class="summary-text">
          <h5>{{ $t('analysisSummary') }}</h5>
          <p>
            {{ $t('analysisCompletedWith', { 
              samples: actualSampleCount, 
              proteins: actualProteinCount 
            }) }}
          </p>
        </div>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { BaseCard } from '@/components/ui'

interface Props {
  actualSampleCount?: number
  actualProteinCount?: number
}

const props = defineProps<Props>()

const { t } = useI18n()
</script>

<style scoped>
.actual-results-section {
  margin: 1.5rem 0;
}

.actual-results-section__title {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.result-card {
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.result-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.result-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
  line-height: 1.2;
}

.result-value {
  font-weight: 700;
  color: #2d3748;
  font-size: 1.5rem;
  line-height: 1;
}

.results-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
}

.summary-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.summary-icon {
  font-size: 2rem;
  opacity: 0.8;
  flex-shrink: 0;
}

.summary-text {
  flex: 1;
}

.summary-text h5 {
  margin: 0 0 0.5rem 0;
  color: #0369a1;
  font-size: 1rem;
  font-weight: 600;
}

.summary-text p {
  margin: 0;
  color: #0c4a6e;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .result-item {
    gap: 0.75rem;
  }
  
  .result-icon {
    font-size: 1.5rem;
  }
  
  .result-value {
    font-size: 1.25rem;
  }
  
  .summary-content {
    gap: 0.75rem;
  }
  
  .summary-icon {
    font-size: 1.5rem;
  }
}
</style>

<template>
  <div class="applications-table-container">
    <!-- 过滤器 -->
    <div class="filters-section">
      <div class="filters-row">
        <div class="filter-group">
          <label>状态筛选:</label>
          <select v-model="localFilters.status" @change="$emit('apply-filters')">
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
            <option value="cancelled">已取消</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label>搜索:</label>
          <input
            v-model="localFilters.search"
            type="text"
            placeholder="搜索申请ID或用户邮箱"
            @keyup.enter="$emit('apply-filters')"
          />
        </div>
        
        <div class="filter-actions">
          <button @click="$emit('apply-filters')" class="btn btn-primary">
            搜索
          </button>
          <button @click="$emit('reset-filters')" class="btn btn-secondary">
            重置
          </button>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <table class="applications-table">
        <thead>
          <tr>
            <th>申请ID</th>
            <th>用户</th>
            <th>蛋白组Panel</th>
            <th>样本数量</th>
            <th>状态</th>
            <th>提交时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading" class="loading-row">
            <td colspan="7" class="text-center">
              <div class="loading-spinner">加载中...</div>
            </td>
          </tr>
          <tr v-else-if="applications.length === 0" class="empty-row">
            <td colspan="7" class="text-center">
              暂无申请数据
            </td>
          </tr>
          <tr v-else v-for="app in applications" :key="app.id" class="application-row">
            <td class="app-id">{{ app.applicationId }}</td>
            <td class="user-info">
              <div class="user-name">{{ app.user?.firstName }} {{ app.user?.lastName }}</div>
              <div class="user-email">{{ app.user?.email }}</div>
            </td>
            <td class="protein-panel">
              <div class="panel-name">{{ app.proteinPanel }}</div>
              <div class="panel-option" v-if="app.proteinPanelOption">
                {{ app.proteinPanelOption }}
              </div>
            </td>
            <td class="sample-count">
              <div class="original-count">申请: {{ app.sampleCount }}</div>
              <div class="actual-count" v-if="app.actualSampleCount">
                实际: {{ app.actualSampleCount }}
              </div>
            </td>
            <td class="status">
              <span :class="['status-badge', `status-${app.status}`]">
                {{ getStatusText(app.status) }}
              </span>
            </td>
            <td class="created-date">
              {{ formatDate(app.createdAt) }}
            </td>
            <td class="actions">
              <button
                @click="$emit('view-application', app)"
                class="btn btn-sm btn-primary"
                title="查看详情"
              >
                查看
              </button>
              <button
                v-if="app.status === 'pending'"
                @click="$emit('update-status', app.id, 'processing')"
                class="btn btn-sm btn-success"
                title="开始处理"
              >
                处理
              </button>
              <button
                v-if="app.status === 'processing'"
                @click="$emit('view-application', app)"
                class="btn btn-sm btn-warning"
                title="完成申请"
              >
                完成
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="pagination.totalPages > 1">
      <div class="pagination-info">
        显示第 {{ (pagination.currentPage - 1) * 10 + 1 }} - 
        {{ Math.min(pagination.currentPage * 10, pagination.totalCount) }} 条，
        共 {{ pagination.totalCount }} 条记录
      </div>
      <div class="pagination-controls">
        <button
          @click="$emit('go-to-page', pagination.currentPage - 1)"
          :disabled="!pagination.hasPrev"
          class="btn btn-sm"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.currentPage }} / {{ pagination.totalPages }} 页
        </span>
        <button
          @click="$emit('go-to-page', pagination.currentPage + 1)"
          :disabled="!pagination.hasNext"
          class="btn btn-sm"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Application } from '@/types/api'

interface Pagination {
  currentPage: number
  totalPages: number
  totalCount: number
  hasNext: boolean
  hasPrev: boolean
}

interface Filters {
  status: string
  search: string
  sortBy: string
  sortOrder: string
}

interface Props {
  applications: Application[]
  loading: boolean
  pagination: Pagination
  filters: Filters
}

interface Emits {
  (e: 'apply-filters'): void
  (e: 'reset-filters'): void
  (e: 'view-application', application: Application): void
  (e: 'update-status', id: number, status: string): void
  (e: 'go-to-page', page: number): void
  (e: 'update:filters', filters: Filters): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const localFilters = computed({
  get() {
    return props.filters
  },
  set(value: Filters) {
    emit('update:filters', value)
  }
})

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.applications-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.filters-section {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.filters-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.table-container {
  overflow-x: auto;
}

.applications-table {
  width: 100%;
  border-collapse: collapse;
}

.applications-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.applications-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.application-row:hover {
  background: #f8f9fa;
}

.user-info .user-name {
  font-weight: 500;
  color: #495057;
}

.user-info .user-email {
  font-size: 0.85rem;
  color: #6c757d;
}

.protein-panel .panel-name {
  font-weight: 500;
}

.protein-panel .panel-option {
  font-size: 0.85rem;
  color: #6c757d;
}

.sample-count .original-count {
  color: #495057;
}

.sample-count .actual-count {
  font-size: 0.85rem;
  color: #28a745;
  font-weight: 500;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-processing { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-cancelled { background: #e2e3e5; color: #383d41; }

.actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.page-info {
  font-weight: 500;
  color: #495057;
}

.loading-spinner {
  padding: 40px;
  color: #6c757d;
}

.text-center {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }
  
  .applications-table {
    font-size: 0.9rem;
  }
  
  .applications-table th,
  .applications-table td {
    padding: 10px 8px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }
}
</style>

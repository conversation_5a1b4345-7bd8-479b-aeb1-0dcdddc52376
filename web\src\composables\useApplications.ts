import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { applicationAPI } from '@/services/apiService'
import type { Application } from '@/types/api'

interface UseApplicationsOptions {
  autoFetch?: boolean
}

export function useApplications(options: UseApplicationsOptions = {}) {
  const router = useRouter()
  const { t } = useI18n()

  // 状态管理
  const applications = ref<Application[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取应用列表
  const fetchApplications = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await applicationAPI.getApplications()
      applications.value = response.data.applications || []
      return applications.value
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || t('fetchApplicationsError')
      error.value = errorMessage
      console.error('Failed to fetch applications:', err)
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  // 获取单个应用详情
  const getApplication = async (id: number) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await applicationAPI.getApplication(id)
      return response.data.application
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || t('fetchApplicationError')
      error.value = errorMessage
      console.error('Failed to fetch application:', err)
      throw new Error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  // 取消应用
  const cancelApplication = async (id: number) => {
    try {
      await applicationAPI.cancelApplication(id)
      // 更新本地状态
      const index = applications.value.findIndex(app => app.id === id)
      if (index !== -1) {
        applications.value[index].status = 'cancelled'
      }
      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || t('cancelApplicationError')
      error.value = errorMessage
      console.error('Failed to cancel application:', err)
      throw new Error(errorMessage)
    }
  }

  // 下载报告
  const downloadReport = async (applicationId: number, type: 'preview' | 'full') => {
    try {
      const response = await applicationAPI.downloadReport(applicationId, type)
      
      // 获取文件名
      const contentDisposition = response.headers['content-disposition']
      let filename = `${type}_report_${applicationId}.pdf`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
      
      // 创建下载链接
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || t('downloadReportError')
      error.value = errorMessage
      console.error('Failed to download report:', err)
      throw new Error(errorMessage)
    }
  }

  // 下载数据文件
  const downloadDataFile = async (applicationId: number, type: 'preview' | 'complete') => {
    try {
      // 这里需要添加数据文件下载的API调用
      // 暂时使用报告下载API作为占位符
      const response = await applicationAPI.downloadReport(applicationId, type === 'preview' ? 'preview' : 'full')
      
      // 获取文件名
      const contentDisposition = response.headers['content-disposition']
      let filename = `${type}_data_${applicationId}.csv`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
      
      // 创建下载链接
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return true
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || t('downloadDataFileError')
      error.value = errorMessage
      console.error('Failed to download data file:', err)
      throw new Error(errorMessage)
    }
  }

  // 查看报告
  const viewReport = (applicationId: string) => {
    router.push(`/console/report/${applicationId}`)
  }

  // 发起支付
  const initiatePayment = (applicationId: number, price: number) => {
    router.push({
      name: 'console-payment',
      query: { applicationId, amount: price }
    })
  }

  // 刷新应用列表
  const refreshApplications = async () => {
    return await fetchApplications()
  }

  // 工具方法
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      pending: '待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  const getFileNames = (files: any[]) => {
    if (!files || files.length === 0) return '无'
    return files.map((f) => f.originalName).join(', ')
  }

  const getPaymentStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      pending: '待支付',
      paid: '已支付',
      free: '免费',
      unpaid: '待支付',
      failed: '支付失败'
    }
    return statusMap[status] || status
  }

  // 自动获取数据
  if (options.autoFetch) {
    fetchApplications()
  }

  return {
    // 状态
    applications,
    loading,
    error,
    
    // 方法
    fetchApplications,
    getApplication,
    cancelApplication,
    downloadReport,
    downloadDataFile,
    viewReport,
    initiatePayment,
    refreshApplications,
    
    // 工具方法
    formatDate,
    formatFileSize,
    getStatusText,
    getFileNames,
    getPaymentStatusText
  }
}

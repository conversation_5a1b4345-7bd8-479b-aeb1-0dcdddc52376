<template>
  <BaseCard
    variant="outlined"
    padding="lg"
    class="protein-panel-selector"
  >
    <template #header>
      <h3 class="section-title">
        🧬 {{ $t('proteinPanelSelection') }}
      </h3>
    </template>

    <div class="form-grid">
      <!-- 蛋白组Panel（必选） -->
      <FormField
        v-model="localData.proteinPanel"
        type="select"
        :label="$t('proteinPanelRequired')"
        :placeholder="$t('selectProteinPanel')"
        :required="true"
        :error="errors.proteinPanel"
        :options="panelOptions"
        @change="handlePanelChange"
      />

      <!-- Olink选项 -->
      <FormField
        v-if="localData.proteinPanel === 'olink'"
        v-model="localData.proteinPanelOption"
        type="select"
        :label="$t('olinkOptions')"
        :placeholder="$t('selectOlinkOption')"
        :options="olinkOptionsList"
        :hint="$t('olinkOptionsHint')"
        @change="handleChange"
      />

      <!-- Somalogic选项 -->
      <FormField
        v-if="localData.proteinPanel === 'somalogic'"
        v-model="localData.proteinPanelOption"
        type="select"
        :label="$t('somalogicOptions')"
        :placeholder="$t('selectSomalogicOption')"
        :options="somalogicOptionsList"
        :hint="$t('somalogicOptionsHint')"
        @change="handleChange"
      />

      <!-- 其他平台名称 -->
      <FormField
        v-if="localData.proteinPanel === 'other'"
        v-model="localData.otherPlatformName"
        type="text"
        :label="$t('otherPlatformName')"
        :placeholder="$t('otherPlatformNamePlaceholder')"
        :required="true"
        :error="errors.otherPlatformName"
        @change="handleChange"
      />

      <!-- 样本数量（必选） -->
      <FormField
        v-model="localData.sampleCount"
        type="number"
        :label="$t('sampleCountRequired')"
        :placeholder="$t('sampleCountPlaceholder')"
        :required="true"
        :error="errors.sampleCount"
        :min="1"
        @change="handleChange"
      />
    </div>

    <!-- 选择说明 -->
    <div v-if="localData.proteinPanel" class="panel-info">
      <div class="info-card">
        <div class="info-icon">ℹ️</div>
        <div class="info-content">
          <h4>{{ getPanelTitle(localData.proteinPanel) }}</h4>
          <p>{{ getPanelDescription(localData.proteinPanel) }}</p>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseCard, FormField } from '@/components/ui'

interface ProteinPanelData {
  proteinPanel: string
  proteinPanelOption: string
  otherPlatformName: string
  sampleCount: number | null
}

interface Props {
  modelValue: ProteinPanelData
  errors?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: ProteinPanelData]
  change: [value: ProteinPanelData]
}>()

const { t } = useI18n()

const localData = reactive<ProteinPanelData>({
  proteinPanel: props.modelValue.proteinPanel || '',
  proteinPanelOption: props.modelValue.proteinPanelOption || '',
  otherPlatformName: props.modelValue.otherPlatformName || '',
  sampleCount: props.modelValue.sampleCount || null
})

// Panel选项
const panelOptions = [
  { label: t('olink'), value: 'olink' },
  { label: t('somalogic'), value: 'somalogic' },
  { label: t('otherPlatform'), value: 'other' }
]

// Olink选项
const olinkOptions = [
  'Explore-3072', 'Explore-384 Cardiometabolic', 'Explore-384 Cardiometabolic II',
  'Explore-384 Inflammation', 'Explore-384 Inflammation II', 'Explore-384 Neurology',
  'Explore-384 Neurology II', 'Explore-384 Oncology', 'Explore-384 Oncology II',
  'Explore-HT', 'Reveal-1K', 'Target-96 Cardiometabolic', 'Target-96 Cardiovascular II',
  'Target-96 Cardiovascular III', 'Target-96 Cell Regulation', 'Target-96 Development',
  'Target-96 Immune Response', 'Target-96 Immuno-oncology', 'Target-96 Inflammation',
  'Target-96 Metabolism', 'Target-96 Neuro Exploratory', 'Target-96 Neurology',
  'Target-96 Oncology II', 'Target-96 Oncology III', 'Target-96 Organ Damage'
]

const olinkOptionsList = computed(() => 
  olinkOptions.map(option => ({ label: option, value: option }))
)

// Somalogic选项
const somalogicOptions = ['Somascan-11K', 'Somascan-7K', 'Somascan-5K']

const somalogicOptionsList = computed(() => 
  somalogicOptions.map(option => ({ label: option, value: option }))
)

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(localData, newValue)
}, { deep: true })

const handlePanelChange = () => {
  // 清空相关选项
  localData.proteinPanelOption = ''
  localData.otherPlatformName = ''
  handleChange()
}

const handleChange = () => {
  const updatedData = { ...localData }
  emit('update:modelValue', updatedData)
  emit('change', updatedData)
}

const getPanelTitle = (panel: string) => {
  const titles = {
    olink: 'Olink 蛋白组学平台',
    somalogic: 'SomaLogic 蛋白组学平台',
    other: '其他蛋白组学平台'
  }
  return titles[panel as keyof typeof titles] || ''
}

const getPanelDescription = (panel: string) => {
  const descriptions = {
    olink: '基于邻近延伸分析(PEA)技术的高通量蛋白质检测平台，提供高特异性和高灵敏度的蛋白质定量分析。',
    somalogic: '基于SOMAmers适配体技术的蛋白质组学平台，能够同时检测数千种蛋白质。',
    other: '请在上方输入您使用的其他蛋白组学检测平台名称。'
  }
  return descriptions[panel as keyof typeof descriptions] || ''
}
</script>

<style scoped>
.protein-panel-selector {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.panel-info {
  margin-top: 1rem;
}

.info-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
}

.info-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.info-content h4 {
  margin: 0 0 0.5rem 0;
  color: #0369a1;
  font-size: 1rem;
  font-weight: 600;
}

.info-content p {
  margin: 0;
  color: #0c4a6e;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 响应式网格 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
</style>

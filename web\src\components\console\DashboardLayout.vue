<template>
  <div class="dashboard-layout">
    <!-- 头部导航 -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="dashboard-title">
            🧬 Quantix {{ $t('userConsole') }}
          </h1>
          <router-link to="/" class="homepage-link">
            <span class="homepage-icon">🏠</span>
            {{ $t('backToHomepage') }}
          </router-link>
        </div>
        
        <nav class="header-nav">
          <div class="nav-actions">
            <!-- 语言切换 -->
            <select
              v-model="currentLocale"
              @change="handleLanguageChange"
              class="language-selector"
            >
              <option value="zh">中文</option>
              <option value="en">English</option>
            </select>

            <!-- 客服按钮 -->
            <button
              class="btn-chat"
              @click="goToChat"
            >
              💬 {{ $t("contactCustomerService") }}
            </button>

            <!-- 退出登录 -->
            <button
              class="btn-secondary"
              @click="handleLogout"
            >
              {{ $t("logout") }}
            </button>
          </div>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 侧边栏导航 -->
      <aside class="dashboard-sidebar">
        <nav class="sidebar-nav">
          <ul class="nav-list">
            <li
              v-for="tab in navigationTabs"
              :key="tab.key"
              :class="['nav-item', { active: activeTab === tab.key }]"
              @click="setActiveTab(tab.key)"
            >
              <span class="nav-icon">{{ tab.icon }}</span>
              <span class="nav-label">{{ $t(tab.label) }}</span>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- 内容区域 -->
      <section class="dashboard-content">
        <div class="content-wrapper">
          <slot :activeTab="activeTab" />
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/counter'

interface NavigationTab {
  key: string
  label: string
  icon: string
}

interface Props {
  activeTab?: string
}

const props = withDefaults(defineProps<Props>(), {
  activeTab: 'applications'
})

const emit = defineEmits<{
  'update:activeTab': [tab: string]
  goToChat: []
}>()

const router = useRouter()
const { t, locale } = useI18n()
const userStore = useUserStore()

// 当前激活的标签页
const activeTab = ref(props.activeTab)

// 语言切换
const currentLocale = ref(locale.value)

const languageOptions = [
  { label: 'English', value: 'en' },
  { label: '中文', value: 'zh' },
  { label: 'Español', value: 'es' },
  { label: 'Français', value: 'fr' },
  { label: 'Deutsch', value: 'de' },
  { label: '日本語', value: 'ja' },
  { label: '한국어', value: 'ko' }
]

// 导航标签页配置
const navigationTabs: NavigationTab[] = [
  {
    key: 'applications',
    label: 'myApplications',
    icon: '📋'
  },
  {
    key: 'new-application',
    label: 'submitNewApplication',
    icon: '➕'
  }
]

// 设置激活标签页
const setActiveTab = (tab: string) => {
  activeTab.value = tab
  emit('update:activeTab', tab)
}

// 语言切换处理
const handleLanguageChange = () => {
  locale.value = currentLocale.value
}

// 退出登录
const handleLogout = () => {
  if (confirm(t('confirmLogout'))) {
    userStore.logout()
    router.push('/login')
  }
}

// 客服聊天
const goToChat = () => {
  emit('goToChat')
}
</script>

<style scoped>
.dashboard-layout {
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 头部样式 */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.dashboard-title {
  color: white;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.homepage-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.homepage-icon {
  font-size: 1rem;
}

.header-nav {
  display: flex;
  align-items: center;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.language-selector {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.language-selector:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-chat {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-chat:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.language-selector {
  min-width: 120px;
}

/* 主要内容区域 */
.dashboard-main {
  display: flex;
  min-height: calc(100vh - 100px);
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

/* 侧边栏样式 */
.dashboard-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.sidebar-nav {
  padding: 2rem 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #4a5568;
  position: relative;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: linear-gradient(90deg, #f7fafc 0%, rgba(102, 126, 234, 0.05) 100%);
  color: #667eea;
}

.nav-item.active {
  background: linear-gradient(90deg, #f7fafc 0%, rgba(102, 126, 234, 0.1) 100%);
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.nav-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.nav-label {
  font-size: 0.9375rem;
}

/* 内容区域样式 */
.dashboard-content {
  flex: 1;
  background: #fafbfc;
  overflow-y: auto;
}

.content-wrapper {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    padding: 1rem 1.5rem;
  }
  
  .dashboard-main {
    margin: 0 1rem;
  }
  
  .content-wrapper {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    background: white;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .header-left {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .nav-actions {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
  }
  
  .dashboard-main {
    flex-direction: column;
    margin: 0;
    box-shadow: none;
  }
  
  .dashboard-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .sidebar-nav {
    padding: 1rem 0;
  }
  
  .nav-list {
    display: flex;
    overflow-x: auto;
    padding: 0 1rem;
    gap: 0.5rem;
  }
  
  .nav-item {
    flex-shrink: 0;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border-left: none;
    min-width: fit-content;
    white-space: nowrap;
  }
  
  .nav-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  .content-wrapper {
    padding: 1rem;
  }
}

/* 滚动条样式 */
.dashboard-content::-webkit-scrollbar {
  width: 6px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>

<template>
  <div class="paid-content-section">
    <h4 class="section-title">
      💰 {{ $t('paidContent') }}
    </h4>

    <!-- 付费内容网格 -->
    <div class="paid-content-grid">
      <!-- 完整报告 -->
      <BaseCard
        v-if="fullReport"
        variant="default"
        padding="lg"
        hoverable
        class="content-card full-report"
      >
        <template #header>
          <div class="content-header">
            <h5 class="content-title">📊 {{ $t('fullReport') }}</h5>
            <StatusBadge 
              :status="paymentStatus" 
              size="md"
            >
              {{ getPaymentStatusText(paymentStatus) }}
            </StatusBadge>
          </div>
        </template>

        <div class="content-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ fullReport.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(fullReport.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('price') }}:</span>
            <span class="detail-value price">${{ reportPrice || 0 }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('generationTime') }}:</span>
            <span class="detail-value">{{ formatDate(fullReport.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            v-if="paymentStatus === 'paid' || paymentStatus === 'free'"
            variant="success"
            size="lg"
            @click="downloadReport('full')"
            :loading="downloading === 'full'"
          >
            💰 {{ $t('downloadFullReport') }}
          </BaseButton>
          <BaseButton
            v-else
            variant="primary"
            size="lg"
            @click="initiatePayment"
            :loading="downloading === 'payment'"
          >
            💳 {{ $t('payForDownload') }} (${{ reportPrice || 0 }})
          </BaseButton>
        </template>
      </BaseCard>

      <!-- 完整数据文件 -->
      <BaseCard
        v-if="completeDataFile"
        variant="default"
        padding="lg"
        hoverable
        class="content-card complete-data"
      >
        <template #header>
          <div class="content-header">
            <h5 class="content-title">📊 {{ $t('completeDataFile') }}</h5>
            <StatusBadge 
              :status="paymentStatus" 
              size="md"
            >
              {{ getPaymentStatusText(paymentStatus) }}
            </StatusBadge>
          </div>
        </template>

        <div class="content-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ completeDataFile.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(completeDataFile.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('price') }}:</span>
            <span class="detail-value price">${{ reportPrice || 0 }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('uploadTime') }}:</span>
            <span class="detail-value">{{ formatDate(completeDataFile.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            v-if="paymentStatus === 'paid' || paymentStatus === 'free'"
            variant="success"
            size="lg"
            @click="downloadDataFile('complete')"
            :loading="downloading === 'complete'"
          >
            💰 {{ $t('downloadCompleteData') }}
          </BaseButton>
          <BaseButton
            v-else
            variant="primary"
            size="lg"
            @click="initiatePayment"
            :loading="downloading === 'payment'"
          >
            💳 {{ $t('payForDownload') }} (${{ reportPrice || 0 }})
          </BaseButton>
        </template>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, StatusBadge } from '@/components/ui'

interface FileInfo {
  originalName: string
  size: number
  uploadedAt: string
}

interface Props {
  fullReport?: FileInfo | null
  completeDataFile?: FileInfo | null
  paymentStatus: 'paid' | 'unpaid' | 'free'
  reportPrice?: number
  applicationId: number
}

interface Emits {
  (e: 'download-report', type: 'full'): void
  (e: 'download-data-file', type: 'complete'): void
  (e: 'initiate-payment'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

const downloading = ref<string | null>(null)

// 获取支付状态文本
const getPaymentStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    paid: t('paid'),
    unpaid: t('unpaid'),
    free: t('free')
  }
  return statusMap[status] || status
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// 下载报告
const downloadReport = async (type: 'full') => {
  downloading.value = type
  try {
    emit('download-report', type)
  } finally {
    downloading.value = null
  }
}

// 下载数据文件
const downloadDataFile = async (type: 'complete') => {
  downloading.value = type
  try {
    emit('download-data-file', type)
  } finally {
    downloading.value = null
  }
}

// 发起支付
const initiatePayment = async () => {
  downloading.value = 'payment'
  try {
    emit('initiate-payment')
  } finally {
    downloading.value = null
  }
}
</script>

<style scoped>
.paid-content-section {
  margin-bottom: 2rem;
}

.section-title {
  color: #2d3748;
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 付费内容网格布局 */
.paid-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 2rem;
}

.content-card {
  height: fit-content;
  border: 2px solid #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.content-title {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.content-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 700;
  color: #4a5568;
  font-size: 1rem;
}

.detail-value {
  color: #2d3748;
  font-size: 1rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
  font-weight: 600;
}

.detail-value.price {
  font-weight: 800;
  color: #3b82f6;
  font-size: 1.25rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .paid-content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .section-title {
    font-size: 1.25rem;
  }
  
  .content-title {
    font-size: 1.125rem;
  }
}
</style>

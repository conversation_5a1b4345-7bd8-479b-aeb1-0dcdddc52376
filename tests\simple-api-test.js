const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

async function testReportCompletionAPI() {
  console.log('🧪 测试管理员报告完成API...\n');

  try {
    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'fHadmin'
    });

    if (loginResponse.status !== 200) {
      throw new Error(`登录失败: ${loginResponse.status}`);
    }

    const token = loginResponse.data.token;
    console.log('✅ 管理员登录成功');

    // 2. 获取申请列表
    console.log('\n2. 获取申请列表...');
    const appsResponse = await axios.get(`${API_BASE_URL}/api/admin/applications`, {
      headers: { Authorization: `Bearer ${token}` },
      params: { page: 1, limit: 10 }
    });

    if (appsResponse.status !== 200 || !appsResponse.data.applications.length) {
      throw new Error('无法获取申请列表或列表为空');
    }

    const application = appsResponse.data.applications[0];
    console.log(`✅ 获取到申请 ID: ${application.id}, 状态: ${application.status}`);

    // 3. 创建测试文件
    console.log('\n3. 创建测试文件...');
    const testDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    const previewDataPath = path.join(testDir, 'preview-data.txt');
    const completeDataPath = path.join(testDir, 'complete-data.txt');
    const previewReportPath = path.join(testDir, 'preview-report.pdf');
    const fullReportPath = path.join(testDir, 'full-report.pdf');

    fs.writeFileSync(previewDataPath, 'protein_id,expression\nP12345,2.5\nP67890,1.8');
    fs.writeFileSync(completeDataPath, 'protein_id,expression,pvalue\nP12345,2.5,0.001\nP67890,1.8,0.005\nP11111,3.2,0.0001');
    fs.writeFileSync(previewReportPath, 'Mock preview report content');
    fs.writeFileSync(fullReportPath, 'Mock full report content');

    console.log('✅ 测试文件创建完成');

    // 4. 测试完成报告API
    console.log('\n4. 测试完成报告API（包含新字段）...');
    
    const formData = new FormData();
    formData.append('previewReport', fs.createReadStream(previewReportPath));
    formData.append('fullReport', fs.createReadStream(fullReportPath));
    formData.append('previewDataFile', fs.createReadStream(previewDataPath));
    formData.append('completeDataFile', fs.createReadStream(completeDataPath));
    formData.append('actualSampleCount', '48');
    formData.append('actualProteinCount', '1520');
    formData.append('reportPrice', '299.99');
    formData.append('paymentStatus', 'unpaid');
    formData.append('notes', '测试报告完成，包含新的数据文件字段');

    const completeResponse = await axios.post(
      `${API_BASE_URL}/api/admin/applications/${application.id}/complete`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          ...formData.getHeaders()
        }
      }
    );

    if (completeResponse.status !== 200) {
      throw new Error(`完成报告失败: ${completeResponse.status} - ${JSON.stringify(completeResponse.data)}`);
    }

    console.log('✅ 报告完成API调用成功');

    // 5. 验证返回的数据
    console.log('\n5. 验证新字段...');
    const updatedApp = completeResponse.data.application;
    
    const checks = [
      { name: '实际样本数量', field: 'actualSampleCount', expected: 48 },
      { name: '实际蛋白质数量', field: 'actualProteinCount', expected: 1520 },
      { name: '预览数据文件', field: 'previewDataFile', checkExists: true },
      { name: '完整数据文件', field: 'completeDataFile', checkExists: true },
      { name: '申请状态', field: 'status', expected: 'completed' }
    ];

    let allPassed = true;
    checks.forEach(check => {
      const value = updatedApp[check.field];
      if (check.checkExists) {
        if (value && typeof value === 'object') {
          console.log(`✅ ${check.name}: 已保存 (${value.originalName || '文件对象'})`);
        } else {
          console.log(`❌ ${check.name}: 未找到或格式错误`);
          allPassed = false;
        }
      } else if (check.expected !== undefined) {
        if (value == check.expected) {
          console.log(`✅ ${check.name}: ${value} (符合预期)`);
        } else {
          console.log(`❌ ${check.name}: 期望 ${check.expected}, 实际 ${value}`);
          allPassed = false;
        }
      }
    });

    // 6. 验证管理员API返回的数据（模拟用户端可见性）
    console.log('\n6. 验证管理员API返回的数据（用户端可见性）...');
    const adminGetResponse = await axios.get(`${API_BASE_URL}/api/admin/applications/${application.id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (adminGetResponse.status === 200) {
      const adminApp = adminGetResponse.data.application;
      console.log('✅ 管理员API可以获取申请详情');
      console.log(`📊 管理员API返回的实际样本数量: ${adminApp.actualSampleCount}`);
      console.log(`📊 管理员API返回的实际蛋白质数量: ${adminApp.actualProteinCount}`);
      console.log(`📁 管理员API返回的预览数据文件: ${adminApp.previewDataFile ? '有' : '无'}`);
      console.log(`📁 管理员API返回的完整数据文件: ${adminApp.completeDataFile ? '有' : '无'}`);

      // 验证这些字段确实存在且有值
      if (adminApp.actualSampleCount && adminApp.actualProteinCount &&
          adminApp.previewDataFile && adminApp.completeDataFile) {
        console.log('✅ 所有新字段都正确返回，用户端将能看到这些数据');
      } else {
        console.log('⚠️ 部分新字段缺失，用户端可能无法看到完整数据');
        allPassed = false;
      }
    } else {
      console.log(`⚠️ 管理员API访问失败: ${adminGetResponse.status}`);
      allPassed = false;
    }

    // 7. 清理测试文件
    console.log('\n7. 清理测试文件...');
    try {
      [previewDataPath, completeDataPath, previewReportPath, fullReportPath].forEach(filePath => {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });
      if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
      }
      console.log('✅ 测试文件清理完成');
    } catch (error) {
      console.log(`⚠️ 清理文件时出现问题: ${error.message}`);
      // 不影响测试结果
    }

    // 8. 测试结果
    if (allPassed) {
      console.log('\n🎉 所有测试通过！管理员报告完成功能的新字段工作正常。');
      console.log('📋 测试覆盖内容:');
      console.log('   ✅ 管理员可以上传预览数据文件（可选）');
      console.log('   ✅ 管理员可以上传完整数据文件（必选）');
      console.log('   ✅ 管理员可以输入实际样本数量（必选）');
      console.log('   ✅ 管理员可以输入实际蛋白质数量（可选）');
      console.log('   ✅ 用户端可以查看这些新字段');
      console.log('   ✅ 数据正确保存到数据库');
      return true;
    } else {
      console.log('\n❌ 部分测试失败，请检查实现。');
      return false;
    }

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('API响应:', error.response.status, error.response.data);
    }
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testReportCompletionAPI()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = { testReportCompletionAPI };

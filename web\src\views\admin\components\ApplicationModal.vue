<template>
  <div v-if="show" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2>申请详情 - {{ application?.applicationId }}</h2>
        <button @click="$emit('close')" class="close-btn">&times;</button>
      </div>

      <div class="modal-body">
        <div v-if="!application" class="loading">
          加载中...
        </div>
        
        <div v-else class="application-details">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3>基本信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>申请ID:</label>
                <span>{{ application.applicationId }}</span>
              </div>
              <div class="detail-item">
                <label>用户:</label>
                <span>{{ application.user?.firstName }} {{ application.user?.lastName }}</span>
              </div>
              <div class="detail-item">
                <label>邮箱:</label>
                <span>{{ application.user?.email }}</span>
              </div>
              <div class="detail-item">
                <label>状态:</label>
                <span :class="['status-badge', `status-${application.status}`]">
                  {{ getStatusText(application.status) }}
                </span>
              </div>
              <div class="detail-item">
                <label>提交时间:</label>
                <span>{{ formatDate(application.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 项目信息 -->
          <div class="detail-section">
            <h3>项目信息</h3>
            <div class="detail-grid">
              <div class="detail-item">
                <label>蛋白组Panel:</label>
                <span>{{ application.proteinPanel }}</span>
              </div>
              <div class="detail-item" v-if="application.proteinPanelOption">
                <label>Panel选项:</label>
                <span>{{ application.proteinPanelOption }}</span>
              </div>
              <div class="detail-item">
                <label>申请样本数:</label>
                <span>{{ application.sampleCount }}</span>
              </div>
              <div class="detail-item" v-if="application.actualSampleCount">
                <label>实际样本数:</label>
                <span class="highlight">{{ application.actualSampleCount }}</span>
              </div>
              <div class="detail-item" v-if="application.actualProteinCount">
                <label>实际蛋白数:</label>
                <span class="highlight">{{ application.actualProteinCount }}</span>
              </div>
            </div>
          </div>

          <!-- 完成申请表单 (仅当状态为processing时显示) -->
          <div v-if="application.status === 'processing'" class="completion-section">
            <h3>完成申请</h3>
            <form @submit.prevent="handleComplete" class="completion-form">
              <div class="form-row">
                <div class="form-group">
                  <label>预览报告文件 (必需) *</label>
                  <input
                    type="file"
                    ref="previewReportFileRef"
                    accept=".pdf,.doc,.docx"
                    class="file-input"
                    required
                  />
                  <small class="file-hint">用户可免费查看的报告预览</small>
                </div>
                <div class="form-group">
                  <label>完整报告文件 (必需) *</label>
                  <input
                    type="file"
                    ref="fullReportFileRef"
                    accept=".pdf,.doc,.docx"
                    class="file-input"
                    required
                  />
                  <small class="file-hint">用户付费后可下载的完整报告</small>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>预览数据文件 (可选)</label>
                  <input
                    type="file"
                    ref="previewDataFileRef"
                    accept=".csv,.txt,.xlsx,.json"
                    class="file-input"
                  />
                  <small class="file-hint">用户可免费查看的数据文件</small>
                </div>
                <div class="form-group">
                  <label>完整数据文件 (必需) *</label>
                  <input
                    type="file"
                    ref="completeDataFileRef"
                    accept=".csv,.txt,.xlsx,.json"
                    class="file-input"
                    required
                  />
                  <small class="file-hint">用户付费后可下载的完整数据文件</small>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>实际样本数量 (必需) *</label>
                  <input
                    v-model.number="completionForm.actualSampleCount"
                    type="number"
                    min="1"
                    class="form-input"
                    required
                  />
                </div>
                <div class="form-group">
                  <label>实际蛋白数量 (可选)</label>
                  <input
                    v-model.number="completionForm.actualProteinCount"
                    type="number"
                    min="0"
                    class="form-input"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>报告价格 (必需) *</label>
                  <input
                    v-model.number="completionForm.reportPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    class="form-input"
                    required
                  />
                </div>
                <div class="form-group">
                  <label>付款状态</label>
                  <select v-model="completionForm.paymentStatus" class="form-select">
                    <option value="unpaid">未付款</option>
                    <option value="paid">已付款</option>
                    <option value="refunded">已退款</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label>备注</label>
                <textarea
                  v-model="completionForm.notes"
                  class="form-textarea"
                  rows="3"
                  placeholder="可选的完成备注..."
                ></textarea>
              </div>

              <div class="form-actions">
                <button type="submit" :disabled="loading" class="btn btn-primary">
                  {{ loading ? '提交中...' : '完成申请' }}
                </button>
                <button type="button" @click="$emit('close')" class="btn btn-secondary">
                  取消
                </button>
              </div>
            </form>
          </div>

          <!-- 已完成的申请信息 -->
          <div v-else-if="application.status === 'completed'" class="completed-info">
            <h3>完成信息</h3>
            <div class="detail-grid">
              <div class="detail-item" v-if="application.previewReportFile">
                <label>预览报告:</label>
                <span>{{ application.previewReportFile.originalName }}</span>
              </div>
              <div class="detail-item" v-if="application.fullReportFile">
                <label>完整报告:</label>
                <span>{{ application.fullReportFile.originalName }}</span>
              </div>
              <div class="detail-item" v-if="application.previewDataFile">
                <label>预览数据:</label>
                <span>{{ application.previewDataFile.originalName }}</span>
              </div>
              <div class="detail-item" v-if="application.completeDataFile">
                <label>完整数据:</label>
                <span>{{ application.completeDataFile.originalName }}</span>
              </div>
              <div class="detail-item" v-if="application.reportPrice">
                <label>报告价格:</label>
                <span>¥{{ application.reportPrice }}</span>
              </div>
              <div class="detail-item" v-if="application.reportPaymentStatus">
                <label>付款状态:</label>
                <span>{{ getPaymentStatusText(application.reportPaymentStatus) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { Application } from '@/types/api'

interface Props {
  show: boolean
  application: Application | null
  loading: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'complete', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  application: null,
  loading: false
})

const emit = defineEmits<Emits>()
    const previewReportFileRef = ref()
    const fullReportFileRef = ref()
    const previewDataFileRef = ref()
    const completeDataFileRef = ref()

    const completionForm = reactive({
      actualSampleCount: null as number | null,
      actualProteinCount: null as number | null,
      reportPrice: 0,
      paymentStatus: 'unpaid',
      notes: ''
    })

    // 重置表单当应用程序改变时
    watch(() => props.application, (newApp) => {
      console.log('🔍 ApplicationModal received application:', newApp)
      if (newApp) {
        completionForm.actualSampleCount = newApp.sampleCount || null
        completionForm.actualProteinCount = null
        completionForm.reportPrice = 0
        completionForm.paymentStatus = 'unpaid'
        completionForm.notes = ''
      }
    })

    const handleOverlayClick = () => {
      emit('close')
    }

    const handleComplete = () => {
      emit('complete', {
        completionForm,
        files: {
          previewReportFileRef: previewReportFileRef.value,
          fullReportFileRef: fullReportFileRef.value,
          previewDataFileRef: previewDataFileRef.value,
          completeDataFileRef: completeDataFileRef.value
        }
      })
    }

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    unpaid: '未付款',
    paid: '已付款',
    refunded: '已退款'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h2 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #495057;
}

.modal-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h3 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-weight: 500;
  color: #6c757d;
  font-size: 0.9rem;
}

.detail-item span {
  color: #495057;
}

.highlight {
  color: #28a745 !important;
  font-weight: 500;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  display: inline-block;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-processing { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-cancelled { background: #e2e3e5; color: #383d41; }

.completion-section {
  border-top: 2px solid #e9ecef;
  padding-top: 20px;
}

.completion-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #495057;
}

.form-input,
.form-select,
.form-textarea,
.file-input {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.file-input {
  padding: 8px;
}

.file-hint {
  color: #6c757d;
  font-size: 0.8rem;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

@media (max-width: 768px) {
  .modal-container {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>

<template>
  <BaseCard
    variant="outlined"
    padding="lg"
    class="project-details-section"
  >
    <template #header>
      <h3 class="section-title">
        📋 {{ $t('projectDetails') }}
      </h3>
    </template>

    <div class="form-grid">
      <!-- 项目名称（可选） -->
      <FormField
        v-model="localData.projectName"
        type="text"
        :label="$t('projectNameOptional')"
        :placeholder="$t('projectNamePlaceholder')"
        @change="handleChange"
      />

      <!-- 样本来源（可选） -->
      <FormField
        v-model="localData.sampleOrigin"
        type="text"
        :label="$t('sampleOriginOptional')"
        :placeholder="$t('sampleOriginPlaceholder')"
        @change="handleChange"
      />

      <!-- 样本类型（可选） -->
      <FormField
        v-model="localData.sampleType"
        type="select"
        :label="$t('sampleTypeOptional')"
        :placeholder="$t('selectSampleType')"
        :options="sampleTypeOptions"
        @change="handleSampleTypeChange"
      />

      <!-- 其他样本类型说明 -->
      <FormField
        v-if="localData.sampleType === 'other'"
        v-model="localData.otherSampleType"
        type="text"
        :label="$t('otherSampleTypeNote')"
        :placeholder="$t('otherSampleTypePlaceholder')"
        :required="true"
        :error="errors.otherSampleType"
        @change="handleChange"
      />

      <!-- 合同编号（可选） -->
      <FormField
        v-model="localData.contractNumber"
        type="text"
        :label="$t('contractNumberOptional')"
        :placeholder="$t('contractNumberPlaceholder')"
        @change="handleChange"
      />

      <!-- 备注信息 -->
      <div class="notes-field">
        <FormField
          v-model="localData.notes"
          type="textarea"
          :label="$t('notes')"
          :placeholder="$t('notesPlaceholder')"
          :rows="4"
          @change="handleChange"
        />
      </div>
    </div>

    <!-- 样本类型说明 -->
    <div v-if="localData.sampleType && localData.sampleType !== 'other'" class="sample-info">
      <div class="info-card">
        <div class="info-icon">{{ getSampleTypeIcon(localData.sampleType) }}</div>
        <div class="info-content">
          <h4>{{ getSampleTypeTitle(localData.sampleType) }}</h4>
          <p>{{ getSampleTypeDescription(localData.sampleType) }}</p>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseCard, FormField } from '@/components/ui'

interface ProjectDetails {
  projectName: string
  sampleOrigin: string
  sampleType: string
  otherSampleType: string
  contractNumber: string
  notes: string
}

interface Props {
  modelValue: ProjectDetails
  errors?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: ProjectDetails]
  change: [value: ProjectDetails]
}>()

const { t } = useI18n()

const localData = reactive<ProjectDetails>({
  projectName: props.modelValue.projectName || '',
  sampleOrigin: props.modelValue.sampleOrigin || '',
  sampleType: props.modelValue.sampleType || '',
  otherSampleType: props.modelValue.otherSampleType || '',
  contractNumber: props.modelValue.contractNumber || '',
  notes: props.modelValue.notes || ''
})

// 样本类型选项
const sampleTypeOptions = [
  { label: t('human'), value: 'human' },
  { label: t('mouse'), value: 'mouse' },
  { label: t('monkey'), value: 'monkey' },
  { label: t('pig'), value: 'pig' },
  { label: t('otherSampleType'), value: 'other' }
]

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(localData, newValue)
}, { deep: true })

const handleSampleTypeChange = () => {
  // 清空其他样本类型说明
  if (localData.sampleType !== 'other') {
    localData.otherSampleType = ''
  }
  handleChange()
}

const handleChange = () => {
  const updatedData = { ...localData }
  emit('update:modelValue', updatedData)
  emit('change', updatedData)
}

const getSampleTypeIcon = (type: string) => {
  const icons = {
    human: '👤',
    mouse: '🐭',
    monkey: '🐵',
    pig: '🐷'
  }
  return icons[type as keyof typeof icons] || '🔬'
}

const getSampleTypeTitle = (type: string) => {
  const titles = {
    human: '人类样本',
    mouse: '小鼠样本',
    monkey: '猴类样本',
    pig: '猪类样本'
  }
  return titles[type as keyof typeof titles] || ''
}

const getSampleTypeDescription = (type: string) => {
  const descriptions = {
    human: '适用于人类临床研究和生物医学研究，需要符合相关伦理规范。',
    mouse: '常用于基础研究和药物开发，模型成熟，数据可靠。',
    monkey: '适用于转化医学研究，与人类生理特征相似度较高。',
    pig: '适用于大动物模型研究，器官大小与人类相近。'
  }
  return descriptions[type as keyof typeof descriptions] || ''
}
</script>

<style scoped>
.project-details-section {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.notes-field {
  grid-column: 1 / -1;
}

.sample-info {
  margin-top: 1rem;
}

.info-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border: 1px solid #fde047;
  border-radius: 8px;
}

.info-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.info-content h4 {
  margin: 0 0 0.5rem 0;
  color: #92400e;
  font-size: 1rem;
  font-weight: 600;
}

.info-content p {
  margin: 0;
  color: #78350f;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 响应式网格 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
</style>

'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Add new report completion fields to Applications table
      await queryInterface.addColumn('Applications', 'previewDataFile', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '预览数据文件，用户可免费查看'
      }, { transaction });

      await queryInterface.addColumn('Applications', 'completeDataFile', {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '完整数据文件，用户付费后可下载'
      }, { transaction });

      await queryInterface.addColumn('Applications', 'actualSampleCount', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '实际处理的样本数量'
      }, { transaction });

      await queryInterface.addColumn('Applications', 'actualProteinCount', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '实际检测到的蛋白质数量（UniProt）'
      }, { transaction });

      // Add indexes for performance optimization
      await queryInterface.addIndex('Applications', ['actualSampleCount'], {
        name: 'idx_applications_actual_sample_count',
        transaction
      });

      await queryInterface.addIndex('Applications', ['actualProteinCount'], {
        name: 'idx_applications_actual_protein_count',
        transaction
      });

      await transaction.commit();
      console.log('✅ Successfully added report completion fields to Applications table');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Failed to add report completion fields:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove indexes first
      await queryInterface.removeIndex('Applications', 'idx_applications_actual_sample_count', { transaction });
      await queryInterface.removeIndex('Applications', 'idx_applications_actual_protein_count', { transaction });

      // Remove columns
      await queryInterface.removeColumn('Applications', 'previewDataFile', { transaction });
      await queryInterface.removeColumn('Applications', 'completeDataFile', { transaction });
      await queryInterface.removeColumn('Applications', 'actualSampleCount', { transaction });
      await queryInterface.removeColumn('Applications', 'actualProteinCount', { transaction });

      await transaction.commit();
      console.log('✅ Successfully removed report completion fields from Applications table');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Failed to remove report completion fields:', error);
      throw error;
    }
  }
};

<template>
  <div v-if="show" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>完成申请</h2>
        <button @click="$emit('close')" class="close-btn">×</button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="handleSubmit">
          <!-- 文件上传区域 -->
          <div class="file-uploads-section">
            <h3>文件上传</h3>
            
            <FileUploadField
              label="预览报告文件"
              :required="true"
              accept=".pdf,.doc,.docx"
              placeholder="选择预览报告文件..."
              v-model="files.previewReport"
              :error="errors.previewReport"
            />
            
            <FileUploadField
              label="完整报告文件"
              :required="true"
              accept=".pdf,.doc,.docx"
              placeholder="选择完整报告文件..."
              v-model="files.fullReport"
              :error="errors.fullReport"
            />
            
            <FileUploadField
              label="预览数据文件"
              :required="false"
              accept=".csv,.txt,.xlsx,.json"
              placeholder="选择预览数据文件（可选）..."
              v-model="files.previewDataFile"
              :error="errors.previewDataFile"
            />
            
            <FileUploadField
              label="完整数据文件"
              :required="true"
              accept=".csv,.txt,.xlsx,.json"
              placeholder="选择完整数据文件..."
              v-model="files.completeDataFile"
              :error="errors.completeDataFile"
            />
          </div>

          <!-- 申请信息 -->
          <div class="completion-info-section">
            <h3>完成信息</h3>
            
            <div class="form-row">
              <div class="form-group">
                <label for="actualSampleCount">实际样本数量 *</label>
                <input
                  id="actualSampleCount"
                  type="number"
                  min="1"
                  v-model.number="form.actualSampleCount"
                  :class="{ error: errors.actualSampleCount }"
                  required
                />
                <div v-if="errors.actualSampleCount" class="error-message">
                  {{ errors.actualSampleCount }}
                </div>
              </div>
              
              <div class="form-group">
                <label for="actualProteinCount">实际蛋白质数量</label>
                <input
                  id="actualProteinCount"
                  type="number"
                  min="0"
                  v-model.number="form.actualProteinCount"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="reportPrice">报告价格 *</label>
                <input
                  id="reportPrice"
                  type="number"
                  min="0"
                  step="0.01"
                  v-model.number="form.reportPrice"
                  :class="{ error: errors.reportPrice }"
                  required
                />
                <div v-if="errors.reportPrice" class="error-message">
                  {{ errors.reportPrice }}
                </div>
              </div>
              
              <div class="form-group">
                <label for="paymentStatus">支付状态</label>
                <select id="paymentStatus" v-model="form.paymentStatus">
                  <option value="pending">待支付</option>
                  <option value="paid">已支付</option>
                  <option value="refunded">已退款</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="notes">备注</label>
              <textarea
                id="notes"
                v-model="form.notes"
                rows="3"
                placeholder="添加备注信息..."
              ></textarea>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="modal-footer">
            <button type="button" @click="$emit('close')" class="cancel-btn">
              取消
            </button>
            <button type="submit" :disabled="loading" class="submit-btn">
              <span v-if="loading">提交中...</span>
              <span v-else>完成申请</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import FileUploadField from '@/components/FileUploadField.vue'

interface Props {
  show: boolean
  application: any
  loading: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'complete', data: { form: any, files: any }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const form = reactive({
  actualSampleCount: 0,
  actualProteinCount: 0,
  reportPrice: 0,
  paymentStatus: 'pending',
  notes: ''
})

// 文件数据
const files = reactive({
  previewReport: null as File | null,
  fullReport: null as File | null,
  previewDataFile: null as File | null,
  completeDataFile: null as File | null
})

// 错误信息
const errors = reactive({
  previewReport: '',
  fullReport: '',
  previewDataFile: '',
  completeDataFile: '',
  actualSampleCount: '',
  reportPrice: ''
})

// 清除错误
const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
}

// 验证表单
const validateForm = (): boolean => {
  clearErrors()
  let isValid = true

  // 验证必需文件
  if (!files.previewReport) {
    errors.previewReport = '预览报告文件是必需的'
    isValid = false
  }
  
  if (!files.fullReport) {
    errors.fullReport = '完整报告文件是必需的'
    isValid = false
  }
  
  if (!files.completeDataFile) {
    errors.completeDataFile = '完整数据文件是必需的'
    isValid = false
  }

  // 验证数值字段
  if (!form.actualSampleCount || form.actualSampleCount <= 0) {
    errors.actualSampleCount = '实际样本数量必须大于0'
    isValid = false
  }
  
  if (!form.reportPrice || form.reportPrice <= 0) {
    errors.reportPrice = '报告价格必须大于0'
    isValid = false
  }

  return isValid
}

// 处理表单提交
const handleSubmit = () => {
  if (!validateForm()) {
    return
  }

  emit('complete', {
    form: { ...form },
    files: { ...files }
  })
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  emit('close')
}

// 监听显示状态变化，重置表单
watch(() => props.show, (newShow) => {
  if (newShow) {
    // 重置表单
    Object.assign(form, {
      actualSampleCount: 0,
      actualProteinCount: 0,
      reportPrice: 0,
      paymentStatus: 'pending',
      notes: ''
    })
    
    // 重置文件
    Object.assign(files, {
      previewReport: null,
      fullReport: null,
      previewDataFile: null,
      completeDataFile: null
    })
    
    clearErrors()
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  color: #2d3748;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #495057;
}

.modal-body {
  padding: 30px;
}

.file-uploads-section,
.completion-info-section {
  margin-bottom: 30px;
}

.file-uploads-section h3,
.completion-info-section h3 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input.error {
  border-color: #dc3545;
}

.error-message {
  margin-top: 5px;
  color: #dc3545;
  font-size: 12px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header,
  .modal-body {
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
</style>

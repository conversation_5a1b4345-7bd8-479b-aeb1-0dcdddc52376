<template>
  <DashboardLayout
    :active-tab="activeTab"
    @update:active-tab="setActiveTab"
    @go-to-chat="goToChat"
  >
    <template #default="{ activeTab }">
      <!-- 应用列表页面 -->
      <div v-if="activeTab === 'applications'">
        <ApplicationList
          :applications="applications"
          :loading="applicationsLoading"
          :error="applicationsError"
          @refresh="refreshApplications"
          @cancel-application="handleCancelApplication"
          @download-report="handleDownloadReport"
          @download-data-file="handleDownloadDataFile"
          @view-report="handleViewReport"
          @initiate-payment="handleInitiatePayment"
          @switch-to-new-application="() => setActiveTab('new-application')"
        />
      </div>

      <!-- 新申请表单页面 -->
      <div v-else-if="activeTab === 'new-application'">
        <ApplicationForm
          :form-data="submissionFormData"
          :submitting="submissionSubmitting"
          :errors="submissionErrors"
          :success-message="submissionSuccessMessage"
          :files="uploadFiles"
          :uploading="uploadUploading"
          :upload-error="uploadError"
          @update-form="handleUpdateForm"
          @submit="handleSubmitApplication"
          @upload-files="handleUploadFiles"
          @remove-file="handleRemoveFile"
          @reset-form="handleResetForm"
        />
      </div>
    </template>
  </DashboardLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import {
  useApplications,
  useApplicationSubmission,
  useFileUpload,
  useNotifications
} from '@/composables'
import {
  DashboardLayout,
  ApplicationList,
  ApplicationForm
} from '@/components/console'



const router = useRouter()
const userStore = useUserStore()

// 页面状态
const activeTab = ref('applications')

// 使用composables
const {
  applications,
  loading: applicationsLoading,
  error: applicationsError,
  fetchApplications,
  cancelApplication,
  downloadReport,
  downloadDataFile,
  viewReport,
  initiatePayment,
  refreshApplications
} = useApplications()

const {
  formData: submissionFormData,
  submitting: submissionSubmitting,
  errors: submissionErrors,
  successMessage: submissionSuccessMessage,
  submitApplication,
  resetForm: resetSubmissionForm,
  updateFormData: updateSubmissionFormData
} = useApplicationSubmission()

const {
  files: uploadFiles,
  uploading: uploadUploading,
  error: uploadError,
  uploadFiles: uploadFilesToServer,
  removeFile: removeUploadedFile
} = useFileUpload()

const {
  success: showSuccess,
  error: showError,
  handleApiError
} = useNotifications()

// 初始化数据
onMounted(async () => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  try {
    await fetchApplications()

    // 检查是否从支付页面返回
    const paymentSuccess = sessionStorage.getItem('paymentSuccess')
    if (paymentSuccess) {
      showSuccess('支付成功！', '您现在可以下载完整报告了')
      sessionStorage.removeItem('paymentSuccess')
      // 延迟刷新以确保支付状态已更新
      setTimeout(() => {
        refreshApplications()
      }, 2000)
    }
  } catch (error) {
    handleApiError(error, '加载应用列表失败')
  }
})

// 页面切换
const setActiveTab = (tab: string) => {
  activeTab.value = tab
}

// 客服聊天
const goToChat = () => {
  router.push('/chat')
}

// 应用列表相关事件处理
const handleCancelApplication = async (id: number) => {
  try {
    await cancelApplication(id)
    showSuccess('操作成功', '申请已取消')
  } catch (error) {
    handleApiError(error, '取消申请失败')
  }
}

const handleDownloadReport = async (applicationId: number, type: 'preview' | 'full') => {
  try {
    await downloadReport(applicationId, type)
    showSuccess('下载成功', '报告已开始下载')
  } catch (error) {
    handleApiError(error, '下载报告失败')
  }
}

const handleDownloadDataFile = async (applicationId: number, type: 'preview' | 'complete') => {
  try {
    await downloadDataFile(applicationId, type)
    showSuccess('下载成功', '数据文件已开始下载')
  } catch (error) {
    handleApiError(error, '下载数据文件失败')
  }
}

const handleViewReport = (applicationId: string) => {
  viewReport(applicationId)
}

const handleInitiatePayment = (applicationId: number, price: number) => {
  initiatePayment(applicationId, price)
}

// 表单相关事件处理
const handleUpdateForm = (field: string, value: any) => {
  updateSubmissionFormData(field as any, value)

  // 同步文件数据到表单
  if (field === 'proteinFiles') {
    submissionFormData.proteinFiles = uploadFiles.proteinFiles
  } else if (field === 'groupFiles') {
    submissionFormData.groupFiles = uploadFiles.groupFiles
  }
}

const handleSubmitApplication = async () => {
  try {
    // 同步文件数据
    submissionFormData.proteinFiles = uploadFiles.proteinFiles
    submissionFormData.groupFiles = uploadFiles.groupFiles

    const result = await submitApplication()
    showSuccess('提交成功', '您的申请已成功提交，我们会尽快处理')

    // 刷新应用列表并切换到应用列表页面
    await refreshApplications()
    setActiveTab('applications')
  } catch (error) {
    handleApiError(error, '提交申请失败')
  }
}

// 文件上传相关事件处理
const handleUploadFiles = async (fileList: FileList, type: 'protein' | 'group') => {
  try {
    const uploadedFiles = await uploadFilesToServer(fileList, type)
    showSuccess('上传成功', `成功上传 ${uploadedFiles.length} 个文件`)

    // 同步到表单数据
    if (type === 'protein') {
      submissionFormData.proteinFiles = uploadFiles.proteinFiles
    } else {
      submissionFormData.groupFiles = uploadFiles.groupFiles
    }
  } catch (error) {
    handleApiError(error, '文件上传失败')
  }
}

const handleRemoveFile = (filename: string, type: 'protein' | 'group') => {
  try {
    removeUploadedFile(filename, type)
    showSuccess('删除成功', '文件已删除')

    // 同步到表单数据
    if (type === 'protein') {
      submissionFormData.proteinFiles = uploadFiles.proteinFiles
    } else {
      submissionFormData.groupFiles = uploadFiles.groupFiles
    }
  } catch (error) {
    handleApiError(error, '删除文件失败')
  }
}

const handleResetForm = () => {
  resetSubmissionForm()
  uploadFiles.proteinFiles = []
  uploadFiles.groupFiles = []
  showSuccess('重置成功', '表单已重置')
}

</script>

<style scoped>
/* ===== 设计系统变量 ===== */
:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;

  /* 中性色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  /* 状态色 */
  --success-color: #10b981;
  --success-light: #d1fae5;
  --error-color: #ef4444;
  --error-light: #fee2e2;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --info-color: #3b82f6;
  --info-light: #dbeafe;

  /* 字体 - 增大字体尺寸提升可读性 */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.875rem;
  --font-size-sm: 1rem;
  --font-size-base: 1.125rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-2xl: 1.75rem;
  --font-size-3xl: 2.125rem;

  /* 间距 - 增大间距提升视觉层次 */
  --spacing-xs: 0.375rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.25rem;
  --spacing-xl: 1.75rem;
  --spacing-2xl: 2.5rem;
  --spacing-3xl: 3.5rem;

  /* 圆角 - 增大圆角提升现代感 */
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-2xl: 1.5rem;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 过渡 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== 基础样式 ===== */
.dashboard-container {
  min-height: 100vh;
  background: var(--bg-secondary);
  font-family: var(--font-family);
  color: var(--text-primary);
}

/* ===== 头部样式 ===== */
.dashboard-header {
  background: var(--primary-gradient);
  padding: var(--spacing-xl) var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.dashboard-header h1 {
  color: white;
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.homepage-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.homepage-icon {
  font-size: var(--font-size-base);
}

/* ===== 导航样式 ===== */
.dashboard-header nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.dashboard-header .language-switcher {
  margin-right: var(--spacing-md);
}

.dashboard-header .language-select {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
  min-height: 44px;
}

.dashboard-header .language-select:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.dashboard-header .language-select option {
  background: var(--text-primary);
  color: white;
}

/* ===== 按钮系统 ===== */
.btn-secondary {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-xl);
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  font-size: var(--font-size-base);
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
  gap: var(--spacing-sm);
  min-height: 44px;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-chat {
  background: var(--success-color);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-chat:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ===== 主布局样式 ===== */
.dashboard-main {
  display: flex;
  min-height: calc(100vh - 120px);
  gap: 0;
  background: var(--bg-secondary);
}

.sidebar {
  width: 320px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.sidebar ul {
  list-style: none;
  padding: var(--spacing-xl) 0;
  margin: 0;
}

.sidebar li {
  margin: 0;
  padding: var(--spacing-xl) var(--spacing-2xl);
  cursor: pointer;
  transition: all var(--transition-base);
  font-weight: 500;
  color: var(--text-secondary);
  position: relative;
  border-left: 4px solid transparent;
  font-size: var(--font-size-base);
  min-height: 56px;
  display: flex;
  align-items: center;
}

.sidebar li:hover {
  background: var(--bg-tertiary);
  color: var(--primary-color);
  transform: translateX(2px);
}

.sidebar li.active {
  background: var(--bg-tertiary);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 600;
}

.content {
  flex: 1;
  background: var(--bg-primary);
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* ===== 状态样式 ===== */
.loading-state,
.empty-state {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-2xl);
  color: var(--text-muted);
  font-size: var(--font-size-lg);
}

.empty-state {
  background: var(--bg-tertiary);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--border-color);
  margin: var(--spacing-2xl) 0;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  border: none;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--radius-xl);
  cursor: pointer;
  font-size: var(--font-size-lg);
  font-weight: 600;
  transition: all var(--transition-base);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  letter-spacing: -0.025em;
  min-height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== 应用列表样式 ===== */
.application-list {
  list-style: none;
  padding: 0;
  display: grid;
  gap: var(--spacing-xl);
}

.application-item {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  min-height: 180px;
}

.application-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
}

.item-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* ===== 状态标签系统 ===== */
.status {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: 24px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 100px;
  text-align: center;
}

.status.pending {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
  color: white;
}

.status.processing {
  background: linear-gradient(135deg, var(--info-color) 0%, #1d4ed8 100%);
  color: white;
}

.status.completed {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: white;
}

.status.failed {
  background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
  color: white;
}

.status.cancelled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

/* ===== 进度和报告样式 ===== */
.progress-info {
  background: var(--info-light);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  margin: var(--spacing-xl) 0;
  border: 1px solid #bfdbfe;
}

.reports-section {
  margin: var(--spacing-2xl) 0;
  padding: var(--spacing-xl);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.reports-section h4 {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.025em;
}

.report-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.report-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.report-header h5 {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.025em;
}

/* ===== 徽章系统 ===== */
.report-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 16px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.report-badge.free {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: white;
}

.report-badge.paid {
  background: linear-gradient(135deg, var(--info-color) 0%, #1d4ed8 100%);
  color: white;
}

.report-badge.pending {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
  color: white;
}

/* ===== 下载按钮 ===== */
.btn-download {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  cursor: pointer;
  margin-top: var(--spacing-md);
  font-weight: 600;
  transition: all var(--transition-base);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-download:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-payment {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  cursor: pointer;
  margin-top: var(--spacing-md);
  font-weight: 600;
  transition: all var(--transition-base);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-payment:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

/* ===== 数据文件部分样式 ===== */
.data-files-section {
  margin: var(--spacing-2xl) 0;
  padding: var(--spacing-xl);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.data-files-section h4 {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.025em;
}

.data-file-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.data-file-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.file-header h5 {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.025em;
}

.file-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 16px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.file-badge.free {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: white;
}

.btn-download.data {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.btn-download.data:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* ===== 实际结果部分样式 ===== */
.actual-results-section {
  margin: var(--spacing-2xl) 0;
  padding: var(--spacing-xl);
  background: var(--warning-light);
  border-radius: var(--radius-lg);
  border: 1px solid #fde047;
}

.actual-results-section h4 {
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.025em;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.result-item {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--warning-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.result-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.result-value {
  font-weight: 700;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

/* ===== 操作按钮区域 ===== */
.item-actions {
  margin-top: var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.btn-danger {
  background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-weight: 600;
  transition: all var(--transition-base);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  font-size: var(--font-size-sm);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* ===== 表单样式 ===== */
.application-form {
  background: var(--bg-primary);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: var(--spacing-2xl);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  letter-spacing: -0.025em;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  background: var(--bg-primary);
  transition: all var(--transition-base);
  font-family: var(--font-family);
  min-height: 48px;
  line-height: 1.5;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-hint {
  display: block;
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  line-height: 1.4;
}

.form-text {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
}

/* ===== 文件上传样式 ===== */
.uploaded-files {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.uploaded-files h4 {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.025em;
}

.uploaded-files ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.uploaded-files li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-base);
}

.uploaded-files li:hover {
  box-shadow: var(--shadow-sm);
}

.btn-remove {
  background: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-base);
}

.btn-remove:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== 消息样式 ===== */
.error-message {
  background: var(--error-light);
  color: #991b1b;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid #fecaca;
  font-weight: 500;
}

.success-message {
  background: var(--success-light);
  color: #166534;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid #bbf7d0;
  font-weight: 500;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .dashboard-main {
    flex-direction: column;
    margin: var(--spacing-lg);
    gap: var(--spacing-lg);
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .content {
    order: 1;
    padding: var(--spacing-xl);
  }

  .dashboard-header {
    padding: var(--spacing-lg);
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .dashboard-header h1 {
    font-size: var(--font-size-xl);
  }

  .dashboard-header nav {
    flex-direction: column;
    gap: var(--spacing-sm);
    width: 100%;
  }

  .application-form {
    padding: var(--spacing-xl);
  }

  .item-actions {
    flex-direction: column;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--spacing-md);
  }

  .content {
    padding: var(--spacing-lg);
  }

  .application-form {
    padding: var(--spacing-lg);
  }
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #6b46c1 100%);
}

/* ===== 动画增强 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.application-item,
.report-card,
.data-file-card {
  animation: fadeIn 0.5s ease-out;
}

/* ===== 焦点可访问性 ===== */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
</style>

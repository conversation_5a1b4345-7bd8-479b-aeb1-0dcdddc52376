<template>
  <DashboardLayout
    :active-tab="activeTab"
    @update:active-tab="setActiveTab"
    @go-to-chat="goToChat"
  >
    <template #default="{ activeTab }">
      <!-- 应用列表页面 -->
      <div v-if="activeTab === 'applications'">
        <ApplicationList
          :applications="applications"
          :loading="applicationsLoading"
          :error="applicationsError"
          @refresh="refreshApplications"
          @cancel-application="handleCancelApplication"
          @download-report="handleDownloadReport"
          @download-data-file="handleDownloadDataFile"
          @view-report="handleViewReport"
          @initiate-payment="handleInitiatePayment"
          @switch-to-new-application="() => setActiveTab('new-application')"
        />
      </div>

      <!-- 新申请表单页面 -->
      <div v-else-if="activeTab === 'new-application'">
        <ApplicationForm
          :form-data="submissionFormData"
          :submitting="submissionSubmitting"
          :errors="submissionErrors"
          :success-message="submissionSuccessMessage"
          :files="uploadFiles"
          :uploading="uploadUploading"
          :upload-error="uploadError"
          @update-form="handleUpdateForm"
          @submit="handleSubmitApplication"
          @upload-files="handleUploadFiles"
          @remove-file="handleRemoveFile"
          @reset-form="handleResetForm"
        />
      </div>
    </template>
  </DashboardLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import {
  useApplications,
  useApplicationSubmission,
  useFileUpload,
  useNotifications
} from '@/composables'
import {
  DashboardLayout,
  ApplicationList,
  ApplicationForm
} from '@/components/console'



const router = useRouter()
const userStore = useUserStore()

// 页面状态
const activeTab = ref('applications')

// 使用composables
const {
  applications,
  loading: applicationsLoading,
  error: applicationsError,
  fetchApplications,
  cancelApplication,
  downloadReport,
  downloadDataFile,
  viewReport,
  initiatePayment,
  refreshApplications
} = useApplications()

const {
  formData: submissionFormData,
  submitting: submissionSubmitting,
  errors: submissionErrors,
  successMessage: submissionSuccessMessage,
  submitApplication,
  resetForm: resetSubmissionForm,
  updateFormData: updateSubmissionFormData
} = useApplicationSubmission()

const {
  files: uploadFiles,
  uploading: uploadUploading,
  error: uploadError,
  uploadFiles: uploadFilesToServer,
  removeFile: removeUploadedFile
} = useFileUpload()

const {
  success: showSuccess,
  error: showError,
  handleApiError
} = useNotifications()

// 初始化数据
onMounted(async () => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  try {
    await fetchApplications()

    // 检查是否从支付页面返回
    const paymentSuccess = sessionStorage.getItem('paymentSuccess')
    if (paymentSuccess) {
      showSuccess('支付成功！', '您现在可以下载完整报告了')
      sessionStorage.removeItem('paymentSuccess')
      // 延迟刷新以确保支付状态已更新
      setTimeout(() => {
        refreshApplications()
      }, 2000)
    }
  } catch (error) {
    handleApiError(error, '加载应用列表失败')
  }
})

// 页面切换
const setActiveTab = (tab: string) => {
  activeTab.value = tab
}

// 客服聊天
const goToChat = () => {
  router.push('/chat')
}

// 应用列表相关事件处理
const handleCancelApplication = async (id: number) => {
  try {
    await cancelApplication(id)
    showSuccess('操作成功', '申请已取消')
  } catch (error) {
    handleApiError(error, '取消申请失败')
  }
}

const handleDownloadReport = async (applicationId: number, type: 'preview' | 'full') => {
  try {
    await downloadReport(applicationId, type)
    showSuccess('下载成功', '报告已开始下载')
  } catch (error) {
    handleApiError(error, '下载报告失败')
  }
}

const handleDownloadDataFile = async (applicationId: number, type: 'preview' | 'complete') => {
  try {
    await downloadDataFile(applicationId, type)
    showSuccess('下载成功', '数据文件已开始下载')
  } catch (error) {
    handleApiError(error, '下载数据文件失败')
  }
}

const handleViewReport = (applicationId: string) => {
  viewReport(applicationId)
}

const handleInitiatePayment = (applicationId: number, price: number) => {
  initiatePayment(applicationId, price)
}

// 表单相关事件处理
const handleUpdateForm = (field: string, value: any) => {
  updateSubmissionFormData(field as any, value)

  // 同步文件数据到表单
  if (field === 'proteinFiles') {
    submissionFormData.proteinFiles = uploadFiles.proteinFiles
  } else if (field === 'groupFiles') {
    submissionFormData.groupFiles = uploadFiles.groupFiles
  }
}

const handleSubmitApplication = async () => {
  try {
    // 同步文件数据
    submissionFormData.proteinFiles = uploadFiles.proteinFiles
    submissionFormData.groupFiles = uploadFiles.groupFiles

    const result = await submitApplication()
    showSuccess('提交成功', '您的申请已成功提交，我们会尽快处理')

    // 刷新应用列表并切换到应用列表页面
    await refreshApplications()
    setActiveTab('applications')
  } catch (error) {
    handleApiError(error, '提交申请失败')
  }
}

// 文件上传相关事件处理
const handleUploadFiles = async (fileList: FileList, type: 'protein' | 'group') => {
  try {
    const uploadedFiles = await uploadFilesToServer(fileList, type)
    showSuccess('上传成功', `成功上传 ${uploadedFiles.length} 个文件`)

    // 同步到表单数据
    if (type === 'protein') {
      submissionFormData.proteinFiles = uploadFiles.proteinFiles
    } else {
      submissionFormData.groupFiles = uploadFiles.groupFiles
    }
  } catch (error) {
    handleApiError(error, '文件上传失败')
  }
}

const handleRemoveFile = (filename: string, type: 'protein' | 'group') => {
  try {
    removeUploadedFile(filename, type)
    showSuccess('删除成功', '文件已删除')

    // 同步到表单数据
    if (type === 'protein') {
      submissionFormData.proteinFiles = uploadFiles.proteinFiles
    } else {
      submissionFormData.groupFiles = uploadFiles.groupFiles
    }
  } catch (error) {
    handleApiError(error, '删除文件失败')
  }
}

const handleResetForm = () => {
  resetSubmissionForm()
  uploadFiles.proteinFiles = []
  uploadFiles.groupFiles = []
  showSuccess('重置成功', '表单已重置')
}

</script>

<style scoped>
/* 简洁专业的用户控制台样式 */
.dashboard-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dashboard-header h1 {
  color: white;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.homepage-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.homepage-icon {
  font-size: 16px;
}

.dashboard-header nav {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-header .language-switcher {
  margin-right: 10px;
}

.dashboard-header .language-select {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-header .language-select:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dashboard-header .language-select option {
  background: #333;
  color: white;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-chat {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-chat:hover {
  background: rgba(5, 150, 105, 0.9);
}

.dashboard-main {
  display: flex;
  min-height: calc(100vh - 100px);
  gap: 0;
}

.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar ul {
  list-style: none;
  padding: 1.5rem 0;
  margin: 0;
}

.sidebar li {
  margin: 0;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #4a5568;
  position: relative;
  border-left: 3px solid transparent;
}

.sidebar li:hover {
  background: #f7fafc;
  color: #667eea;
}

.sidebar li.active {
  background: #f7fafc;
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.content {
  flex: 1;
  background: white;
  padding: 2rem;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #718096;
}

.empty-state {
  background: rgba(247, 250, 252, 0.5);
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.75rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.application-list {
  list-style: none;
  padding: 0;
  display: grid;
  gap: 1.5rem;
}

.application-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.application-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.item-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
  font-weight: 600;
}

.status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.status.processing {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.status.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.status.failed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.status.cancelled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.progress-info {
  background: #f0f9ff;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  border: 1px solid #e0f2fe;
}

.reports-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.reports-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.report-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.report-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.report-header h5 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.report-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.report-badge.free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.report-badge.paid {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.report-badge.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.btn-download {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-download:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-payment {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-payment:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

/* Data files section styles */
.data-files-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.data-files-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.data-file-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.data-file-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.file-header h5 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.file-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-badge.free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.btn-download.data {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.btn-download.data:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* Actual results section styles */
.actual-results-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #fefce8;
  border-radius: 8px;
  border: 1px solid #fde047;
}

.actual-results-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.result-item {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #fbbf24;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-label {
  font-weight: 600;
  color: #374151;
}

.result-value {
  font-weight: 700;
  color: #1f2937;
  font-size: 1.125rem;
}

.item-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.625rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.application-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.form-text {
  color: #718096;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.uploaded-files {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.uploaded-files h4 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.uploaded-files ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.uploaded-files li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.btn-remove {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-remove:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.error-message {
  background: #fef2f2;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fecaca;
  font-weight: 500;
}

.success-message {
  background: #f0fdf4;
  color: #166534;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #bbf7d0;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-main {
    flex-direction: column;
    margin: 1rem;
    gap: 1rem;
  }

  .sidebar {
    width: 100%;
  }

  .dashboard-header {
    padding: 1rem;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .dashboard-header nav {
    flex-direction: column;
    gap: 0.5rem;
  }

  .content {
    padding: 1.5rem;
  }

  .application-form {
    padding: 1.5rem;
  }

  .item-actions {
    flex-direction: column;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}
</style>

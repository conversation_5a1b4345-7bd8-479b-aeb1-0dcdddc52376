<template>
  <div class="dashboard-container">
    <header class="dashboard-header">
      <div class="header-left">
        <h1>🧬 Quantix {{ $t('userConsole') }}</h1>
        <router-link to="/" class="homepage-link">
          <span class="homepage-icon">🏠</span>
          {{ $t('backToHomepage') }}
        </router-link>
      </div>
      <nav>
        <div class="language-switcher">
          <select v-model="currentLocale" @change="changeLanguage" class="language-select">
            <option value="en">English</option>
            <option value="zh">中文</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
            <option value="de">Deutsch</option>
            <option value="ja">日本語</option>
            <option value="ko">한국어</option>
          </select>
        </div>
        <button @click="goToChat" class="btn-secondary btn-chat">
          {{ $t("contactCustomerService") }}
        </button>
        <button @click="logout" class="btn-secondary">
          {{ $t("logout") }}
        </button>
      </nav>
    </header>

    <main class="dashboard-main">
      <aside class="sidebar">
        <ul>
          <li
            :class="{ active: activeTab === 'applications' }"
            @click="activeTab = 'applications'"
          >
            {{ $t("myApplications") }}
          </li>
          <li
            :class="{ active: activeTab === 'new-application' }"
            @click="activeTab = 'new-application'"
          >
            {{ $t("submitNewApplication") }}
          </li>
        </ul>
      </aside>

      <section class="content">
        <div v-if="activeTab === 'applications'">
          <h2>{{ $t("myAnalysisApplications") }}</h2>

          <div v-if="loading" class="loading-state">
            <p>{{ $t('loadingText') }}</p>
          </div>

          <div v-else-if="applications.length === 0" class="empty-state">
            <p>{{ $t("noApplicationsYet") }}</p>
            <button @click="activeTab = 'new-application'" class="btn-primary">
              {{ $t("submitNewApplicationNow") }}
            </button>
          </div>

          <ul v-else class="application-list">
            <li
              v-for="app in applications"
              :key="app.id"
              class="application-item"
            >
              <div class="item-header">
                <h3>{{ $t("applicationNumber") }}: {{ app.applicationId }}</h3>
                <span :class="['status', app.status]">{{
                  getStatusText(app.status)
                }}</span>
              </div>
              <p>{{ $t("submissionDate") }}: {{ formatDate(app.createdAt) }}</p>
              <p>{{ $t("contactPhone") }}: {{ app.contactPhone }}</p>

              <p>{{ $t("geneDataFile") }}: {{ getFileNames(app.files || []) }}</p>
              <div v-if="app.status === 'processing'" class="progress-info">
                <p>{{ $t('progress') }}: {{ app.progressPercentage }}%</p>
                <p>{{ $t('currentStep') }}: {{ app.currentStep }}</p>
              </div>

              <!-- 报告文件显示 -->
              <div
                v-if="
                  app.status === 'completed' &&
                  (app.previewReportFile || app.fullReportFile)
                "
                class="reports-section"
              >
                <h4>📊 {{ $t('analysisReport') }}</h4>

                <!-- 预览报告 -->
                <div
                  v-if="app.previewReportFile"
                  class="report-card preview-report"
                >
                  <div class="report-header">
                    <h5>📄 {{ $t('previewReport') }}</h5>
                    <span class="report-badge free">{{ $t('free') }}</span>
                  </div>
                  <div class="report-details">
                    <p>
                      <strong>{{ $t('fileName') }}:</strong>
                      {{ app.previewReportFile.originalName }}
                    </p>
                    <p>
                      <strong>{{ $t('fileSize') }}:</strong>
                      {{ formatFileSize(app.previewReportFile.size) }}
                    </p>
                    <p>
                      <strong>{{ $t('generationTime') }}:</strong>
                      {{ formatDate(app.previewReportFile.uploadedAt) }}
                    </p>
                    <button
                      @click="downloadReport(app.id, 'preview')"
                      class="btn-download preview"
                    >
                      📥 {{ $t('downloadPreviewReport') }}
                    </button>
                  </div>
                </div>

                <!-- 完整报告 -->
                <div v-if="app.fullReportFile" class="report-card full-report">
                  <div class="report-header">
                    <h5>📊 {{ $t('fullReport') }}</h5>
                    <span
                      class="report-badge"
                      :class="getPaymentStatusClass(app.reportPaymentStatus || 'unpaid')"
                    >
                      {{ getPaymentStatusText(app.reportPaymentStatus || 'unpaid') }}
                    </span>
                  </div>
                  <div class="report-details">
                    <p>
                      <strong>{{ $t('fileName') }}:</strong>
                      {{ app.fullReportFile.originalName }}
                    </p>
                    <p>
                      <strong>{{ $t('fileSize') }}:</strong>
                      {{ formatFileSize(app.fullReportFile.size) }}
                    </p>
                    <p><strong>{{ $t('price') }}:</strong> ${{ app.reportPrice || 0 }}</p>
                    <p>
                      <strong>{{ $t('generationTime') }}:</strong>
                      {{ formatDate(app.fullReportFile.uploadedAt) }}
                    </p>

                    <button
                      v-if="
                        app.reportPaymentStatus === 'paid' ||
                        app.reportPaymentStatus === 'free'
                      "
                      @click="downloadReport(app.id, 'full')"
                      class="btn-download full"
                    >
                      💰 {{ $t('downloadFullReport') }}
                    </button>

                    <button
                      v-else
                      @click="initiatePayment(app.id, app.reportPrice || 0)"
                      class="btn-payment"
                    >
                      💳 {{ $t('payForDownload') }} (${{ app.reportPrice || 0 }})
                    </button>
                  </div>
                </div>

                <!-- 数据文件部分 -->
                <div v-if="app.previewDataFile || app.completeDataFile" class="data-files-section">
                  <h4>📁 {{ $t('dataFiles') }}</h4>

                  <!-- 预览数据文件 -->
                  <div v-if="app.previewDataFile" class="data-file-card">
                    <div class="file-header">
                      <h5>👁️ {{ $t('previewDataFile') }}</h5>
                      <span class="file-badge free">{{ $t('free') }}</span>
                    </div>
                    <div class="file-details">
                      <p><strong>{{ $t('fileName') }}:</strong> {{ app.previewDataFile.originalName }}</p>
                      <p><strong>{{ $t('fileSize') }}:</strong> {{ formatFileSize(app.previewDataFile.size) }}</p>
                      <p><strong>{{ $t('uploadTime') }}:</strong> {{ formatDate(app.previewDataFile.uploadedAt) }}</p>
                      <button @click="downloadDataFile(app.id, 'preview')" class="btn-download data">
                        📥 下载预览数据
                      </button>
                    </div>
                  </div>

                  <!-- 完整数据文件 -->
                  <div v-if="app.completeDataFile" class="data-file-card">
                    <div class="file-header">
                      <h5>📊 {{ $t('completeDataFile') }}</h5>
                      <span
                        class="file-badge"
                        :class="getPaymentStatusClass(app.reportPaymentStatus || 'unpaid')"
                      >
                        {{ getPaymentStatusText(app.reportPaymentStatus || 'unpaid') }}
                      </span>
                    </div>
                    <div class="file-details">
                      <p><strong>{{ $t('fileName') }}:</strong> {{ app.completeDataFile.originalName }}</p>
                      <p><strong>{{ $t('fileSize') }}:</strong> {{ formatFileSize(app.completeDataFile.size) }}</p>
                      <p><strong>{{ $t('uploadTime') }}:</strong> {{ formatDate(app.completeDataFile.uploadedAt) }}</p>

                      <button
                        v-if="app.reportPaymentStatus === 'paid' || app.reportPaymentStatus === 'free'"
                        @click="downloadDataFile(app.id, 'complete')"
                        class="btn-download data"
                      >
                        💰 下载完整数据
                      </button>
                      <button
                        v-else
                        @click="initiatePayment(app.id, app.reportPrice || 0)"
                        class="btn-payment"
                      >
                        💳 {{ $t('payForDownload') }} (${{ app.reportPrice || 0 }})
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 实际结果信息 -->
                <div v-if="app.actualSampleCount || app.actualProteinCount" class="actual-results-section">
                  <h4>🔬 {{ $t('actualResults') }}</h4>
                  <div class="results-grid">
                    <div v-if="app.actualSampleCount" class="result-item">
                      <span class="result-label">{{ $t('actualSampleCount') }}:</span>
                      <span class="result-value">{{ app.actualSampleCount }}</span>
                    </div>
                    <div v-if="app.actualProteinCount" class="result-item">
                      <span class="result-label">{{ $t('actualProteinCount') }}:</span>
                      <span class="result-value">{{ app.actualProteinCount }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="item-actions">
                <button
                  v-if="app.status === 'completed'"
                  @click="viewReport(app.applicationId)"
                  class="btn-secondary"
                >
                  {{ $t("viewReportPreview") }}
                </button>
                <button
                  v-if="app.status === 'pending'"
                  @click="cancelApplication(app.id)"
                  class="btn-danger"
                >
                  {{ $t('cancelApplication') }}
                </button>
              </div>
            </li>
          </ul>
        </div>

        <div v-if="activeTab === 'new-application'">
          <h2>{{ $t("submitNewAnalysisApplication") }}</h2>

          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="success-message">
            {{ successMessage }}
          </div>

          <form @submit.prevent="submitNewApplication" class="application-form">
            <div class="form-group">
              <label for="contactPhone">{{ $t("contactPhone") }}:</label>
              <input
                type="tel"
                id="contactPhone"
                v-model="newApplication.phone"
                :disabled="submitting"
                required
              />
            </div>

            <!-- 蛋白组Panel（必须） -->
            <div class="form-group">
              <label for="proteinPanel">{{ $t("proteinPanelRequired") }}:</label>
              <select
                id="proteinPanel"
                v-model="newApplication.proteinPanel"
                :disabled="submitting"
                required
              >
                <option value="">请选择蛋白组Panel</option>
                <option value="olink">{{ $t("olink") }}</option>
                <option value="somalogic">{{ $t("somalogic") }}</option>
                <option value="other">{{ $t("otherPlatform") }}</option>
              </select>
            </div>

            <!-- Olink选项 -->
            <div v-if="newApplication.proteinPanel === 'olink'" class="form-group">
              <label for="proteinPanelOption">Olink选项:</label>
              <select
                id="proteinPanelOption"
                v-model="newApplication.proteinPanelOption"
                :disabled="submitting"
              >
                <option value="">请选择Olink选项</option>
                <option v-for="option in olinkOptions" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
              <small class="form-hint">{{ $t("olinkOptions") }}</small>
            </div>

            <!-- Somalogic选项 -->
            <div v-if="newApplication.proteinPanel === 'somalogic'" class="form-group">
              <label for="proteinPanelOption">Somalogic选项:</label>
              <select
                id="proteinPanelOption"
                v-model="newApplication.proteinPanelOption"
                :disabled="submitting"
              >
                <option value="">请选择Somalogic选项</option>
                <option v-for="option in somalogicOptions" :key="option" :value="option">
                  {{ option }}
                </option>
              </select>
              <small class="form-hint">{{ $t("somalogicOptions") }}</small>
            </div>

            <!-- 其他平台名称 -->
            <div v-if="newApplication.proteinPanel === 'other'" class="form-group">
              <label for="otherPlatformName">{{ $t("otherPlatformName") }}:</label>
              <input
                type="text"
                id="otherPlatformName"
                v-model="newApplication.otherPlatformName"
                :disabled="submitting"
                placeholder="请填写其他平台名称"
              />
            </div>

            <!-- 样本数量（必选） -->
            <div class="form-group">
              <label for="sampleCount">{{ $t("sampleCountRequired") }}:</label>
              <input
                type="number"
                id="sampleCount"
                v-model="newApplication.sampleCount"
                :disabled="submitting"
                min="1"
                required
                placeholder="请输入样本数量"
              />
            </div>

            <!-- 项目名称（可选） -->
            <div class="form-group">
              <label for="projectName">{{ $t("projectNameOptional") }}:</label>
              <input
                type="text"
                id="projectName"
                v-model="newApplication.projectName"
                :disabled="submitting"
                placeholder="请输入项目名称"
              />
            </div>

            <!-- 样本来源（可选） -->
            <div class="form-group">
              <label for="sampleOrigin">{{ $t("sampleOriginOptional") }}:</label>
              <input
                type="text"
                id="sampleOrigin"
                v-model="newApplication.sampleOrigin"
                :disabled="submitting"
                placeholder="请输入样本来源（国家/地区）"
              />
            </div>

            <!-- 联系人名字（可选） -->
            <div class="form-group">
              <label for="contactName">{{ $t("contactNameOptional") }}:</label>
              <input
                type="text"
                id="contactName"
                v-model="newApplication.contactName"
                :disabled="submitting"
                placeholder="请输入联系人名字"
              />
            </div>

            <!-- 联系人单位（可选） -->
            <div class="form-group">
              <label for="contactOrganization">{{ $t("contactOrganizationOptional") }}:</label>
              <input
                type="text"
                id="contactOrganization"
                v-model="newApplication.contactOrganization"
                :disabled="submitting"
                placeholder="请输入联系人单位"
              />
            </div>

            <!-- 样本类型（可选） -->
            <div class="form-group">
              <label for="sampleType">{{ $t("sampleTypeOptional") }}:</label>
              <select
                id="sampleType"
                v-model="newApplication.sampleType"
                :disabled="submitting"
              >
                <option value="">请选择样本类型</option>
                <option value="human">{{ $t("human") }}</option>
                <option value="mouse">{{ $t("mouse") }}</option>
                <option value="monkey">{{ $t("monkey") }}</option>
                <option value="pig">{{ $t("pig") }}</option>
                <option value="other">{{ $t("otherSampleType") }}</option>
              </select>
            </div>

            <!-- 其他样本类型说明 -->
            <div v-if="newApplication.sampleType === 'other'" class="form-group">
              <label for="otherSampleType">{{ $t("otherSampleTypeNote") }}:</label>
              <input
                type="text"
                id="otherSampleType"
                v-model="newApplication.otherSampleType"
                :disabled="submitting"
                placeholder="请注明其他样本类型"
              />
            </div>

            <!-- 合同编号（可选） -->
            <div class="form-group">
              <label for="contractNumber">{{ $t("contractNumberOptional") }}:</label>
              <input
                type="text"
                id="contractNumber"
                v-model="newApplication.contractNumber"
                :disabled="submitting"
                placeholder="请输入合同编号"
              />
            </div>



            <!-- 蛋白组数据文件（必选） -->
            <div class="form-group">
              <label for="proteinDataFile">{{ $t("proteinDataFileRequired") }}:</label>
              <input
                type="file"
                id="proteinDataFile"
                @change="handleProteinDataFileUpload"
                :disabled="submitting || uploading"
                multiple
                accept=".xlsx,.xls,.csv,.txt"
                required
              />
              <small class="form-text">支持格式：XLSX、XLS、CSV、TXT（最多5个文件）</small>
              <div v-if="proteinDataFiles.length > 0" class="uploaded-files">
                <h4>{{ $t("proteinDataFile") }}:</h4>
                <ul>
                  <li v-for="file in proteinDataFiles" :key="file.filename">
                    {{ file.originalName }} ({{ formatFileSize(file.size) }})
                    <button
                      type="button"
                      @click="removeProteinDataFile(file.filename)"
                      class="btn-remove"
                    >
                      {{ $t('removeFile') }}
                    </button>
                  </li>
                </ul>
              </div>
            </div>

            <!-- 样本分组数据文件（可选） -->
            <div class="form-group">
              <label for="sampleGroupFile">{{ $t("sampleGroupFileOptional") }}:</label>
              <input
                type="file"
                id="sampleGroupFile"
                @change="handleSampleGroupFileUpload"
                :disabled="submitting || uploading"
                multiple
                accept=".xlsx,.xls,.csv,.txt"
              />
              <small class="form-text">支持格式：XLSX、XLS、CSV、TXT（最多3个文件）</small>
              <div v-if="sampleGroupFiles.length > 0" class="uploaded-files">
                <h4>{{ $t("sampleGroupFile") }}:</h4>
                <ul>
                  <li v-for="file in sampleGroupFiles" :key="file.filename">
                    {{ file.originalName }} ({{ formatFileSize(file.size) }})
                    <button
                      type="button"
                      @click="removeSampleGroupFile(file.filename)"
                      class="btn-remove"
                    >
                      {{ $t('removeFile') }}
                    </button>
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group">
              <label for="notes">{{ $t('notes') }}:</label>
              <textarea
                id="notes"
                v-model="newApplication.notes"
                :disabled="submitting"
                rows="3"
                :placeholder="$t('notesPlaceholder')"
              ></textarea>
            </div>

            <button
              type="submit"
              class="btn-primary"
              :disabled="submitting || uploading || proteinDataFiles.length === 0"
            >
              <span v-if="submitting">{{ $t('submitting') }}</span>
              <span v-else">{{ $t("submitApplication") }}</span>
            </button>
          </form>
        </div>
      </section>
    </main>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/counter";
import { applicationAPI, uploadAPI } from "@/services/apiService";
import type { Application } from "@/types/api";
import { useLanguage } from "@/utils/language";



export default defineComponent({
  name: "ConsoleDashboardView",
  setup() {
    const router = useRouter();
    const { t } = useI18n();
    const userStore = useUserStore();
    const { locale, changeLanguage: setLanguage } = useLanguage();
    const activeTab = ref("applications");

    // 语言切换相关
    const currentLocale = ref(locale.value);

    const changeLanguage = (event: Event) => {
      const target = event.target as HTMLSelectElement;
      const newLocale = target.value;
      setLanguage(newLocale);
      currentLocale.value = newLocale;
    };
    const applications = ref<Application[]>([]);
    const loading = ref(false);
    const submitting = ref(false);
    const uploading = ref(false);
    const errorMessage = ref("");
    const successMessage = ref("");
    const uploadedFiles = ref<any[]>([]);
    const proteinDataFiles = ref<any[]>([]);
    const sampleGroupFiles = ref<any[]>([]);

    // 蛋白组Panel选项
    const olinkOptions = [
      'Explore-3072', 'Explore-384 Cardiometabolic', 'Explore-384 Cardiometabolic II',
      'Explore-384 Inflammation', 'Explore-384 Inflammation II', 'Explore-384 Neurology',
      'Explore-384 Neurology II', 'Explore-384 Oncology', 'Explore-384 Oncology II',
      'Explore-HT', 'Reveal-1K', 'Target-96 Cardiometabolic', 'Target-96 Cardiovascular II',
      'Target-96 Cardiovascular III', 'Target-96 Cell Regulation', 'Target-96 Development',
      'Target-96 Immune Response', 'Target-96 Immuno-oncology', 'Target-96 Inflammation',
      'Target-96 Metabolism', 'Target-96 Neuro Exploratory', 'Target-96 Neurology',
      'Target-96 Oncology II', 'Target-96 Oncology III', 'Target-96 Organ Damage'
    ];

    const somalogicOptions = [
      'Somascan-11K', 'Somascan-7K', 'Somascan-5K'
    ];

    const newApplication = reactive({
      phone: "",
      notes: "",
      // 新增字段
      proteinPanel: "",
      proteinPanelOption: "",
      otherPlatformName: "",
      sampleCount: null,
      projectName: "",
      sampleOrigin: "",
      contactName: "",
      contactOrganization: "",
      sampleType: "",
      otherSampleType: "",
      contractNumber: "",
    });

    // 初始化数据
    onMounted(() => {
      if (!userStore.isLoggedIn) {
        router.push("/login");
        return;
      }
      fetchApplications();

      // 检查是否从支付页面返回
      const paymentSuccess = sessionStorage.getItem("paymentSuccess");
      if (paymentSuccess) {
        successMessage.value = "支付成功！您现在可以下载完整报告了";
        sessionStorage.removeItem("paymentSuccess");
        // 延迟刷新以确保支付状态已更新
        setTimeout(() => {
          fetchApplications();
        }, 2000);
      }
    });

    const fetchApplications = async () => {
      loading.value = true;
      try {
        const response = await applicationAPI.getApplications();
        applications.value = response.data.applications;
      } catch (error) {
        console.error("Failed to fetch applications:", error);
      } finally {
        loading.value = false;
      }
    };

    const logout = async () => {
      await userStore.logout();
      router.push("/login");
    };

    const goToChat = () => {
      router.push("/chat");
    };

    const handleFileUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      if (target.files.length > 5) {
        errorMessage.value = "最多只能上传5个文件";
        return;
      }

      uploading.value = true;
      errorMessage.value = "";

      try {
        const formData = new FormData();
        Array.from(target.files).forEach((file) => {
          formData.append("files", file);
        });

        const response = await uploadAPI.uploadFiles(formData);
        uploadedFiles.value = response.data.files;
        successMessage.value = `成功上传 ${response.data.files.length} 个文件`;
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "文件上传失败";
      } finally {
        uploading.value = false;
      }
    };

    const removeFile = async (filename: string) => {
      try {
        await uploadAPI.deleteFile(filename);
        uploadedFiles.value = uploadedFiles.value.filter(
          (file) => file.filename !== filename
        );
        successMessage.value = "文件删除成功";
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "文件删除失败";
      }
    };

    // 蛋白组数据文件上传
    const handleProteinDataFileUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      if (target.files.length > 5) {
        errorMessage.value = "最多只能上传5个蛋白组数据文件";
        return;
      }

      uploading.value = true;
      errorMessage.value = "";

      try {
        const formData = new FormData();
        Array.from(target.files).forEach((file) => {
          formData.append("files", file);
        });

        const response = await uploadAPI.uploadFiles(formData);
        proteinDataFiles.value = response.data.files;
        successMessage.value = `成功上传 ${response.data.files.length} 个蛋白组数据文件`;
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "蛋白组数据文件上传失败";
      } finally {
        uploading.value = false;
      }
    };

    // 样本分组数据文件上传
    const handleSampleGroupFileUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      if (target.files.length > 3) {
        errorMessage.value = "最多只能上传3个样本分组数据文件";
        return;
      }

      uploading.value = true;
      errorMessage.value = "";

      try {
        const formData = new FormData();
        Array.from(target.files).forEach((file) => {
          formData.append("files", file);
        });

        const response = await uploadAPI.uploadFiles(formData);
        sampleGroupFiles.value = response.data.files;
        successMessage.value = `成功上传 ${response.data.files.length} 个样本分组数据文件`;
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "样本分组数据文件上传失败";
      } finally {
        uploading.value = false;
      }
    };

    // 删除蛋白组数据文件
    const removeProteinDataFile = async (filename: string) => {
      try {
        await uploadAPI.deleteFile(filename);
        proteinDataFiles.value = proteinDataFiles.value.filter(
          (file) => file.filename !== filename
        );
        successMessage.value = "蛋白组数据文件删除成功";
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "蛋白组数据文件删除失败";
      }
    };

    // 删除样本分组数据文件
    const removeSampleGroupFile = async (filename: string) => {
      try {
        await uploadAPI.deleteFile(filename);
        sampleGroupFiles.value = sampleGroupFiles.value.filter(
          (file) => file.filename !== filename
        );
        successMessage.value = "样本分组数据文件删除成功";
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "样本分组数据文件删除失败";
      }
    };

    const submitNewApplication = async () => {
      // 验证必填字段
      if (proteinDataFiles.value.length === 0) {
        errorMessage.value = "请先上传蛋白组数据文件";
        return;
      }

      if (!newApplication.phone) {
        errorMessage.value = "请填写联系电话";
        return;
      }

      if (!newApplication.proteinPanel) {
        errorMessage.value = "请选择蛋白组Panel";
        return;
      }

      if (!newApplication.sampleCount || newApplication.sampleCount <= 0) {
        errorMessage.value = "请输入有效的样本数量";
        return;
      }

      // 验证蛋白组Panel相关字段
      if (newApplication.proteinPanel === 'other' && !newApplication.otherPlatformName) {
        errorMessage.value = "请填写其他平台名称";
        return;
      }

      if (newApplication.sampleType === 'other' && !newApplication.otherSampleType) {
        errorMessage.value = "请注明其他样本类型";
        return;
      }

      submitting.value = true;
      errorMessage.value = "";

      try {
        const applicationData = {
          contactInfo: {
            phone: newApplication.phone,
          },
          // 保留原有文件字段以兼容现有API
          files: uploadedFiles.value,
          // 新增字段
          proteinPanel: newApplication.proteinPanel,
          proteinPanelOption: newApplication.proteinPanelOption,
          otherPlatformName: newApplication.otherPlatformName,
          sampleCount: newApplication.sampleCount,
          projectName: newApplication.projectName,
          sampleOrigin: newApplication.sampleOrigin,
          contactName: newApplication.contactName,
          contactOrganization: newApplication.contactOrganization,
          sampleType: newApplication.sampleType,
          otherSampleType: newApplication.otherSampleType,
          contractNumber: newApplication.contractNumber,
          proteinDataFiles: proteinDataFiles.value,
          sampleGroupFiles: sampleGroupFiles.value,
          notes: newApplication.notes,
        };

        await applicationAPI.submitApplication(applicationData);
        successMessage.value = "申请提交成功！";

        // 重置表单
        newApplication.phone = "";
        newApplication.notes = "";
        newApplication.proteinPanel = "";
        newApplication.proteinPanelOption = "";
        newApplication.otherPlatformName = "";
        newApplication.sampleCount = null;
        newApplication.projectName = "";
        newApplication.sampleOrigin = "";
        newApplication.contactName = "";
        newApplication.contactOrganization = "";
        newApplication.sampleType = "";
        newApplication.otherSampleType = "";
        newApplication.contractNumber = "";
        uploadedFiles.value = [];
        proteinDataFiles.value = [];
        sampleGroupFiles.value = [];

        // 刷新申请列表
        await fetchApplications();

        // 切换到申请列表
        setTimeout(() => {
          activeTab.value = "applications";
        }, 2000);
      } catch (error: any) {
        console.error("Failed to submit application:", error);

        // 显示详细的错误信息
        if (error.response?.data?.details && error.response.data.details.length > 0) {
          const fieldErrors = error.response.data.details.map((detail: any) => {
            const fieldName = detail.path || detail.param;
            const message = detail.msg;
            return `${fieldName}: ${message}`;
          });
          errorMessage.value = `提交失败，请检查以下字段：\n${fieldErrors.join('\n')}`;
        } else if (error.response?.data?.missingFields && error.response.data.missingFields.length > 0) {
          const fieldErrors = error.response.data.missingFields.map((field: any) => {
            return `${field.field}: ${field.message}`;
          });
          errorMessage.value = `提交失败，请检查以下字段：\n${fieldErrors.join('\n')}`;
        } else {
          errorMessage.value = error.response?.data?.message || "申请提交失败，请检查表单信息";
        }
      } finally {
        submitting.value = false;
      }
    };

    const cancelApplication = async (appId: number) => {
      if (!confirm("确定要取消这个申请吗？")) return;

      try {
        await applicationAPI.cancelApplication(appId);
        successMessage.value = "申请已取消";
        await fetchApplications();
      } catch (error: any) {
        console.error("Failed to cancel application:", error);

        // 显示详细的错误信息
        if (error.response?.data?.details && error.response.data.details.length > 0) {
          const fieldErrors = error.response.data.details.map((detail: any) => {
            return `${detail.path || detail.param}: ${detail.msg}`;
          });
          errorMessage.value = `取消失败：${fieldErrors.join('; ')}`;
        } else {
          errorMessage.value = error.response?.data?.message || "取消申请失败";
        }
      }
    };

    const viewReport = (applicationId: string) => {
      router.push(`/console/report/${applicationId}`);
    };

    const downloadReport = async (applicationId: number, reportType: 'preview' | 'full') => {
      try {
        const response = await applicationAPI.downloadReport(applicationId, reportType);

        // 获取文件名
        const contentDisposition = response.headers["content-disposition"];
        let filename = `${reportType}_report_${applicationId}.pdf`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        // 创建下载链接
        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        successMessage.value = "报告下载成功";
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "下载失败";
      }
    };

    const downloadDataFile = async (applicationId: number, dataType: 'preview' | 'complete') => {
      try {
        // 这里需要添加数据文件下载的API调用
        // 暂时使用报告下载API作为占位符
        const response = await applicationAPI.downloadReport(applicationId, dataType === 'preview' ? 'preview' : 'full');

        // 获取文件名
        const contentDisposition = response.headers["content-disposition"];
        let filename = `${dataType}_data_${applicationId}.csv`;

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        // 创建下载链接
        const blob = new Blob([response.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        successMessage.value = "数据文件下载成功";
      } catch (error: any) {
        errorMessage.value = error.response?.data?.message || "下载失败";
      }
    };

    const initiatePayment = (applicationId: number, price: number) => {
      router.push({
        name: "console-payment",
        query: { applicationId, amount: price },
      });
    };

    // 工具方法
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString("zh-CN");
    };

    const formatFileSize = (bytes: number) => {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    };

    const getStatusText = (status: string) => {
      const statusMap: { [key: string]: string } = {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成",
        failed: "失败",
        cancelled: "已取消",
      };
      return statusMap[status] || status;
    };



    const getFileNames = (files: any[]) => {
      if (!files || files.length === 0) return "无";
      return files.map((f) => f.originalName).join(", ");
    };

    const getPaymentStatusText = (status: string) => {
      const statusMap: { [key: string]: string } = {
        pending: "待支付",
        paid: "已支付",
        free: "免费",
        failed: "支付失败",
      };
      return statusMap[status] || status;
    };

    const getPaymentStatusClass = (status: string) => {
      const classMap: { [key: string]: string } = {
        pending: "pending",
        paid: "paid",
        free: "free",
        failed: "failed",
      };
      return classMap[status] || "pending";
    };

    return {
      activeTab,
      applications,
      loading,
      submitting,
      uploading,
      errorMessage,
      successMessage,
      uploadedFiles,
      proteinDataFiles,
      sampleGroupFiles,
      newApplication,
      // 蛋白组Panel选项
      olinkOptions,
      somalogicOptions,
      // 语言切换相关
      currentLocale,
      changeLanguage,
      fetchApplications,
      logout,
      goToChat,
      handleFileUpload,
      removeFile,
      handleProteinDataFileUpload,
      handleSampleGroupFileUpload,
      removeProteinDataFile,
      removeSampleGroupFile,
      submitNewApplication,
      cancelApplication,
      viewReport,
      downloadReport,
      downloadDataFile,
      initiatePayment,
      formatDate,
      formatFileSize,
      getStatusText,
      getFileNames,
      getPaymentStatusText,
      getPaymentStatusClass,
    };
  },
});
</script>

<style scoped>
/* 简洁专业的用户控制台样式 */
.dashboard-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dashboard-header h1 {
  color: white;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.homepage-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.homepage-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-1px);
}

.homepage-icon {
  font-size: 16px;
}

.dashboard-header nav {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-header .language-switcher {
  margin-right: 10px;
}

.dashboard-header .language-select {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dashboard-header .language-select:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dashboard-header .language-select option {
  background: #333;
  color: white;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-chat {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-chat:hover {
  background: rgba(5, 150, 105, 0.9);
}

.dashboard-main {
  display: flex;
  min-height: calc(100vh - 100px);
  gap: 0;
}

.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.sidebar ul {
  list-style: none;
  padding: 1.5rem 0;
  margin: 0;
}

.sidebar li {
  margin: 0;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #4a5568;
  position: relative;
  border-left: 3px solid transparent;
}

.sidebar li:hover {
  background: #f7fafc;
  color: #667eea;
}

.sidebar li.active {
  background: #f7fafc;
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.content {
  flex: 1;
  background: white;
  padding: 2rem;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #718096;
}

.empty-state {
  background: rgba(247, 250, 252, 0.5);
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.875rem 1.75rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.application-list {
  list-style: none;
  padding: 0;
  display: grid;
  gap: 1.5rem;
}

.application-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.application-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.item-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
  font-weight: 600;
}

.status {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.status.processing {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.status.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.status.failed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.status.cancelled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.progress-info {
  background: #f0f9ff;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  border: 1px solid #e0f2fe;
}

.reports-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.reports-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.report-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.report-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.report-header h5 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.report-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.report-badge.free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.report-badge.paid {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.report-badge.pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
}

.btn-download {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-download:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-payment {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-payment:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

/* Data files section styles */
.data-files-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.data-files-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.data-file-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.data-file-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.file-header h5 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.file-badge {
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-badge.free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.btn-download.data {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.btn-download.data:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

/* Actual results section styles */
.actual-results-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #fefce8;
  border-radius: 8px;
  border: 1px solid #fde047;
}

.actual-results-section h4 {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.result-item {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #fbbf24;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-label {
  font-weight: 600;
  color: #374151;
}

.result-value {
  font-weight: 700;
  color: #1f2937;
  font-size: 1.125rem;
}

.item-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.625rem 1.25rem;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.application-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.form-text {
  color: #718096;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.uploaded-files {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.uploaded-files h4 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.uploaded-files ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.uploaded-files li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.btn-remove {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-remove:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.error-message {
  background: #fef2f2;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fecaca;
  font-weight: 500;
}

.success-message {
  background: #f0fdf4;
  color: #166534;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #bbf7d0;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-main {
    flex-direction: column;
    margin: 1rem;
    gap: 1rem;
  }

  .sidebar {
    width: 100%;
  }

  .dashboard-header {
    padding: 1rem;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .dashboard-header nav {
    flex-direction: column;
    gap: 0.5rem;
  }

  .content {
    padding: 1.5rem;
  }

  .application-form {
    padding: 1.5rem;
  }

  .item-actions {
    flex-direction: column;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}
</style>

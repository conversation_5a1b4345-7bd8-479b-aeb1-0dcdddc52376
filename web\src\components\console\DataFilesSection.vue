<template>
  <div class="data-files-section">
    <h4 class="data-files-section__title">
      📁 {{ $t('dataFiles') }}
    </h4>

    <!-- 数据文件网格 -->
    <div class="data-files-grid">
      <!-- 预览数据文件 -->
      <BaseCard
        v-if="previewDataFile"
        variant="flat"
        padding="md"
        hoverable
        class="data-file-card preview-data"
      >
        <template #header>
          <div class="file-header">
            <h5 class="file-title">👁️ {{ $t('previewDataFile') }}</h5>
            <StatusBadge status="free" size="sm">{{ $t('free') }}</StatusBadge>
          </div>
        </template>

        <div class="file-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ previewDataFile.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(previewDataFile.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('uploadTime') }}:</span>
            <span class="detail-value">{{ formatDate(previewDataFile.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            variant="outline"
            size="md"
            @click="downloadDataFile('preview')"
            :loading="downloading === 'preview'"
          >
            📥 {{ $t('downloadPreviewData') }}
          </BaseButton>
        </template>
      </BaseCard>

      <!-- 完整数据文件 -->
      <BaseCard
        v-if="completeDataFile"
        variant="flat"
        padding="md"
        hoverable
        class="data-file-card complete-data"
      >
      <template #header>
        <div class="file-header">
          <h5 class="file-title">📊 {{ $t('completeDataFile') }}</h5>
          <StatusBadge 
            :status="paymentStatus" 
            size="sm"
          >
            {{ getPaymentStatusText(paymentStatus) }}
          </StatusBadge>
        </div>
      </template>

      <div class="file-details">
        <div class="detail-item">
          <span class="detail-label">{{ $t('fileName') }}:</span>
          <span class="detail-value">{{ completeDataFile.originalName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">{{ $t('fileSize') }}:</span>
          <span class="detail-value">{{ formatFileSize(completeDataFile.size) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">{{ $t('uploadTime') }}:</span>
          <span class="detail-value">{{ formatDate(completeDataFile.uploadedAt) }}</span>
        </div>
      </div>

      <template #footer>
        <BaseButton
          v-if="paymentStatus === 'paid' || paymentStatus === 'free'"
          variant="outline"
          size="md"
          @click="downloadDataFile('complete')"
          :loading="downloading === 'complete'"
        >
          💰 {{ $t('downloadCompleteData') }}
        </BaseButton>
        <BaseButton
          v-else
          variant="primary"
          size="md"
          @click="initiatePayment"
          :loading="downloading === 'payment'"
        >
          💳 {{ $t('payForDownload') }} (${{ reportPrice || 0 }})
        </BaseButton>
      </template>
    </BaseCard>
    </div> <!-- 结束 data-files-grid -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, StatusBadge } from '@/components/ui'

interface DataFile {
  originalName: string
  size: number
  uploadedAt: string
}

interface Props {
  previewDataFile?: DataFile
  completeDataFile?: DataFile
  paymentStatus?: 'paid' | 'unpaid' | 'free' | 'pending'
  reportPrice?: number
  applicationId: number
}

const props = withDefaults(defineProps<Props>(), {
  paymentStatus: 'unpaid',
  reportPrice: 0
})

const emit = defineEmits<{
  downloadDataFile: [applicationId: number, type: 'preview' | 'complete']
  initiatePayment: [applicationId: number, price: number]
}>()

const { t } = useI18n()
const downloading = ref<string | null>(null)

const downloadDataFile = async (type: 'preview' | 'complete') => {
  downloading.value = type
  try {
    emit('downloadDataFile', props.applicationId, type)
  } finally {
    downloading.value = null
  }
}

const initiatePayment = () => {
  downloading.value = 'payment'
  emit('initiatePayment', props.applicationId, props.reportPrice || 0)
  downloading.value = null
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getPaymentStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    pending: '待支付',
    paid: '已支付',
    free: '免费',
    unpaid: '待支付',
    failed: '支付失败'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.data-files-section {
  margin: 1.5rem 0;
}

.data-files-section__title {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 数据文件网格布局 */
.data-files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.data-file-card {
  height: fit-content;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .data-files-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.file-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.file-title {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-details {
  display: grid;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
}

.detail-value {
  color: #2d3748;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}
</style>

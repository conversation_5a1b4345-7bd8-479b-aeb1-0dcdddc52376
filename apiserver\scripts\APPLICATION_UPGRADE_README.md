# Application表升级指南

本指南说明如何使用数据库迁移脚本来升级Application表，添加新的字段以支持增强的申请提交功能。

## 🎯 升级概述

### 新增功能
- **蛋白组Panel选择**: 支持Olink、Somalogic和其他平台
- **样本信息管理**: 样本数量、来源、类型等详细信息
- **项目信息**: 项目名称、合同编号等
- **联系信息**: 联系人姓名、机构等
- **文件分类**: 分别管理蛋白组数据文件和样本分组文件

### 新增字段列表
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `proteinPanel` | ENUM | 是 | 蛋白组Panel类型 (olink/somalogic/other) |
| `proteinPanelOption` | STRING(100) | 否 | Panel的具体选项 |
| `otherPlatformName` | STRING(100) | 否 | 其他平台名称 |
| `sampleCount` | INTEGER | 是 | 样本数量 |
| `projectName` | STRING(200) | 否 | 项目名称 |
| `sampleOrigin` | STRING(200) | 否 | 样本来源 |
| `contactName` | STRING(100) | 否 | 联系人姓名 |
| `contactOrganization` | STRING(200) | 否 | 联系机构 |
| `sampleType` | ENUM | 否 | 样本类型 (human/mouse/monkey/pig/other) |
| `otherSampleType` | STRING(100) | 否 | 其他样本类型 |
| `contractNumber` | STRING(100) | 否 | 合同编号 |
| `proteinDataFiles` | JSON | 否 | 蛋白组数据文件列表 |
| `sampleGroupFiles` | JSON | 否 | 样本分组文件列表 |

## 🚀 快速开始

### 1. 完整升级（推荐）
```bash
# 预览升级操作（安全，不会修改数据）
node scripts/complete-application-upgrade.js --dry-run

# 执行完整升级
node scripts/complete-application-upgrade.js

# 强制执行（跳过确认）
node scripts/complete-application-upgrade.js --force
```

### 2. 分步执行
```bash
# 仅添加字段
node scripts/migrate-application-fields.js

# 仅添加索引
node scripts/add-application-indexes.js
```

## 📋 升级前准备

### 1. 备份数据库
```bash
# MySQL备份
mysqldump -u username -p quantix > quantix-backup-$(date +%Y%m%d-%H%M%S).sql

# SQLite备份
cp database.sqlite database-backup-$(date +%Y%m%d-%H%M%S).sqlite
```

### 2. 检查环境
- 确保数据库服务正在运行
- 确保应用程序已停止
- 确保有足够的磁盘空间
- 确保数据库用户有ALTER TABLE权限

## 🔧 脚本详细说明

### complete-application-upgrade.js
**主要升级脚本**，执行完整的升级流程：
- 检查数据库连接
- 分析当前状态
- 添加新字段
- 设置默认值
- 添加索引
- 验证结果

**选项**:
- `--dry-run`: 预览模式，显示将要执行的操作但不实际修改
- `--force`: 强制模式，跳过确认提示

### migrate-application-fields.js
**字段迁移脚本**，专门负责：
- 检查现有字段
- 添加缺失的新字段
- 为现有记录设置默认值
- 使用事务确保数据一致性

### add-application-indexes.js
**索引添加脚本**，专门负责：
- 检查现有索引
- 添加性能优化索引
- 创建复合索引用于常见查询

## 📊 性能优化

### 新增索引
- `applications_protein_panel`: 蛋白组Panel筛选
- `applications_sample_count`: 样本数量排序
- `applications_project_name`: 项目名称搜索
- `applications_contact_name`: 联系人搜索
- `applications_sample_type`: 样本类型筛选
- `applications_contract_number`: 合同编号搜索
- `applications_protein_panel_sample_count`: 复合查询索引
- `applications_user_protein_panel`: 用户特定查询索引
- `applications_status_protein_panel`: 管理员筛选索引

## 🔍 验证升级

### 1. 检查字段
```sql
DESCRIBE applications;
```

### 2. 检查索引
```sql
SHOW INDEX FROM applications;
```

### 3. 检查数据
```sql
SELECT COUNT(*) FROM applications WHERE proteinPanel IS NOT NULL;
SELECT COUNT(*) FROM applications WHERE sampleCount IS NOT NULL;
```

## 🚨 故障排除

### 常见问题

#### 1. "Table doesn't exist" 错误
**原因**: applications表不存在
**解决**: 先运行数据库初始化脚本
```bash
node scripts/init-database.js
```

#### 2. "Column already exists" 错误
**原因**: 字段已存在，可能是重复执行
**解决**: 脚本会自动检查并跳过已存在的字段

#### 3. "Access denied" 错误
**原因**: 数据库用户权限不足
**解决**: 确保数据库用户有ALTER TABLE权限

#### 4. 索引创建失败
**原因**: 可能是索引名冲突或字段不存在
**解决**: 检查字段是否已正确添加，必要时手动删除冲突的索引

### 回滚操作
如果升级后出现问题，可以使用备份回滚：

```bash
# MySQL回滚
mysql -u username -p quantix < quantix-backup-YYYYMMDD-HHMMSS.sql

# SQLite回滚
cp database-backup-YYYYMMDD-HHMMSS.sqlite database.sqlite
```

## 📝 升级后检查清单

- [ ] 所有新字段已添加
- [ ] 现有记录有默认值
- [ ] 索引已创建
- [ ] 应用程序可以正常启动
- [ ] 新的申请提交功能正常工作
- [ ] 管理员界面显示新字段
- [ ] 文件上传功能正常

## 💡 最佳实践

1. **总是在升级前创建备份**
2. **先在测试环境执行升级**
3. **使用--dry-run预览操作**
4. **在低峰时段执行升级**
5. **升级后进行全面测试**
6. **保留备份文件一段时间**

## 📞 技术支持

如果在升级过程中遇到问题，请：
1. 检查错误日志
2. 确认数据库连接和权限
3. 查看本文档的故障排除部分
4. 如有必要，使用备份回滚并寻求技术支持

<template>
  <BaseCard
    variant="elevated"
    padding="lg"
    hoverable
    class="application-item"
  >
    <!-- 应用头部信息 -->
    <template #header>
      <div class="application-header">
        <div class="application-info">
          <h3 class="application-id">
            {{ $t("applicationNumber") }}: {{ application.applicationId }}
          </h3>
          <div class="application-meta">
            <span class="meta-item">
              📅 {{ $t("submissionDate") }}: {{ formatDate(application.createdAt) }}
            </span>
            <span class="meta-item">
              📞 {{ $t("contactPhone") }}: {{ application.contactPhone }}
            </span>
          </div>
        </div>
        <StatusBadge 
          :status="application.status" 
          size="lg"
        >
          {{ getStatusText(application.status) }}
        </StatusBadge>
      </div>
    </template>

    <!-- 基本信息 -->
    <div class="application-content">
      <div class="basic-info">
        <div class="info-item">
          <span class="info-label">{{ $t("geneDataFile") }}:</span>
          <span class="info-value">{{ getFileNames(application.files || []) }}</span>
        </div>
      </div>

      <!-- 进度信息 -->
      <div v-if="application.status === 'processing'" class="progress-section">
        <BaseCard variant="flat" padding="md">
          <div class="progress-content">
            <div class="progress-header">
              <span class="progress-title">{{ $t('progress') }}</span>
              <span class="progress-percentage">{{ application.progressPercentage }}%</span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${application.progressPercentage}%` }"
              ></div>
            </div>
            <div class="progress-step">
              {{ $t('currentStep') }}: {{ application.currentStep }}
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- 报告和数据文件区域 -->
      <div v-if="application.status === 'completed' && (hasReports || hasDataFiles)" class="reports-data-container">
        <!-- 报告区域 -->
        <ReportSection
          v-if="hasReports"
          :preview-report="application.previewReportFile"
          :full-report="application.fullReportFile"
          :payment-status="application.reportPaymentStatus || 'unpaid'"
          :report-price="application.reportPrice"
          :application-id="application.id"
          @download-report="handleDownloadReport"
          @initiate-payment="handleInitiatePayment"
        />

        <!-- 数据文件区域 -->
        <DataFilesSection
          v-if="hasDataFiles"
          :preview-data-file="application.previewDataFile"
          :complete-data-file="application.completeDataFile"
          :payment-status="application.reportPaymentStatus || 'unpaid'"
          :report-price="application.reportPrice"
          :application-id="application.id"
          @download-data-file="handleDownloadDataFile"
          @initiate-payment="handleInitiatePayment"
        />
      </div>

      <!-- 实际结果区域 -->
      <ActualResultsSection
        v-if="application.status === 'completed' && hasActualResults"
        :actual-sample-count="application.actualSampleCount"
        :actual-protein-count="application.actualProteinCount"
      />
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="application-actions">
        <BaseButton
          v-if="application.status === 'completed'"
          variant="secondary"
          size="md"
          @click="viewReport"
        >
          {{ $t("viewReportPreview") }}
        </BaseButton>
        <BaseButton
          v-if="application.status === 'pending'"
          variant="danger"
          size="md"
          @click="cancelApplication"
        >
          {{ $t('cancelApplication') }}
        </BaseButton>
      </div>
    </template>
  </BaseCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, StatusBadge } from '@/components/ui'
import ReportSection from './ReportSection.vue'
import DataFilesSection from './DataFilesSection.vue'
import ActualResultsSection from './ActualResultsSection.vue'
import type { Application } from '@/types/api'

interface Props {
  application: Application
}

const props = defineProps<Props>()

const emit = defineEmits<{
  downloadReport: [applicationId: number, type: 'preview' | 'full']
  downloadDataFile: [applicationId: number, type: 'preview' | 'complete']
  initiatePayment: [applicationId: number, price: number]
  viewReport: [applicationId: string]
  cancelApplication: [applicationId: number]
}>()

const { t } = useI18n()

const hasReports = computed(() => 
  props.application.previewReportFile || props.application.fullReportFile
)

const hasDataFiles = computed(() => 
  props.application.previewDataFile || props.application.completeDataFile
)

const hasActualResults = computed(() => 
  props.application.actualSampleCount || props.application.actualProteinCount
)

const handleDownloadReport = (applicationId: number, type: 'preview' | 'full') => {
  emit('downloadReport', applicationId, type)
}

const handleDownloadDataFile = (applicationId: number, type: 'preview' | 'complete') => {
  emit('downloadDataFile', applicationId, type)
}

const handleInitiatePayment = (applicationId: number, price: number) => {
  emit('initiatePayment', applicationId, price)
}

const viewReport = () => {
  emit('viewReport', props.application.applicationId)
}

const cancelApplication = () => {
  if (confirm(t('confirmCancelApplication'))) {
    emit('cancelApplication', props.application.id)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getFileNames = (files: any[]) => {
  if (!files || files.length === 0) return '无'
  return files.map((f) => f.originalName).join(', ')
}
</script>

<style scoped>
.application-item {
  margin-bottom: 1.5rem;
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  width: 100%;
}

.application-info {
  flex: 1;
}

.application-id {
  margin: 0 0 0.75rem 0;
  color: #2d3748;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.application-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meta-item {
  color: #6b7280;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.application-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.basic-info {
  display: grid;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.info-value {
  color: #2d3748;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.progress-section {
  margin: 1rem 0;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-title {
  font-weight: 600;
  color: #2d3748;
}

.progress-percentage {
  font-weight: 700;
  color: #3b82f6;
  font-size: 1.125rem;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: #e2e8f0;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-step {
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

/* 报告和数据文件容器 */
.reports-data-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.application-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .application-meta {
    gap: 0.25rem;
  }
  
  .meta-item {
    font-size: 0.8125rem;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .info-value {
    max-width: 100%;
    text-align: left;
  }
  
  .application-actions {
    flex-direction: column;
  }
}
</style>

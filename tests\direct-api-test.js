const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

// 直接测试API端点结构
async function testDirectAPI() {
  console.log('🧪 直接测试API端点结构...\n');

  try {
    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const adminLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'fHadmin'
    });

    if (adminLoginResponse.status !== 200) {
      console.log(`❌ 管理员登录失败: ${adminLoginResponse.status}`);
      return false;
    }

    console.log('✅ 管理员登录成功');
    const adminToken = adminLoginResponse.data.token;

    // 2. 获取申请详情（管理员API）
    console.log('\n2. 测试管理员API...');
    const adminAppsResponse = await axios.get(`${API_BASE_URL}/api/admin/applications`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    if (adminAppsResponse.data.applications.length > 0) {
      const adminApp = adminAppsResponse.data.applications[0];
      console.log('✅ 管理员API申请列表结构:');
      console.log(`   - actualSampleCount: ${adminApp.hasOwnProperty('actualSampleCount') ? '✅' : '❌'} (值: ${adminApp.actualSampleCount || 'null'})`);
      console.log(`   - actualProteinCount: ${adminApp.hasOwnProperty('actualProteinCount') ? '✅' : '❌'} (值: ${adminApp.actualProteinCount || 'null'})`);
      console.log(`   - previewDataFile: ${adminApp.hasOwnProperty('previewDataFile') ? '✅' : '❌'} (值: ${adminApp.previewDataFile ? 'object' : 'null'})`);
      console.log(`   - completeDataFile: ${adminApp.hasOwnProperty('completeDataFile') ? '✅' : '❌'} (值: ${adminApp.completeDataFile ? 'object' : 'null'})`);
    }

    // 3. 创建测试用户
    console.log('\n3. 创建测试用户...');
    const testUserEmail = `testuser${Date.now()}@example.com`;
    const testUserPassword = 'testpass123';

    try {
      const registerResponse = await axios.post(`${API_BASE_URL}/api/auth/register`, {
        email: testUserEmail,
        password: testUserPassword,
        firstName: 'Test',
        lastName: 'User'
      });
      console.log('✅ 测试用户创建成功');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('⚠️ 用户可能已存在，继续测试');
      } else {
        throw error;
      }
    }

    // 4. 用户登录
    console.log('\n4. 用户登录...');
    const userLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: testUserEmail,
      password: testUserPassword
    });

    if (userLoginResponse.status !== 200) {
      console.log(`❌ 用户登录失败: ${userLoginResponse.status}`);
      return false;
    }

    console.log('✅ 用户登录成功');
    const userToken = userLoginResponse.data.token;

    // 5. 创建测试申请
    console.log('\n5. 创建测试申请...');
    const createAppResponse = await axios.post(`${API_BASE_URL}/api/applications`, {
      contactInfo: { phone: '************' },
      proteinPanel: 'olink',
      proteinPanelOption: 'Olink Explore 1536',
      sampleCount: 10,
      proteinDataFiles: [{
        originalName: 'test-data.txt',
        filename: 'test-data.txt',
        path: '/uploads/test-data.txt',
        size: 1024,
        fileType: 'text/plain'
      }],
      groupingFiles: [],
      projectDetails: {
        title: 'Test Project',
        description: 'Test Description',
        objectives: 'Test Objectives'
      },
      sampleTypes: ['plasma'],
      contractNumber: 'TEST001'
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    if (createAppResponse.status !== 201) {
      console.log(`❌ 创建申请失败: ${createAppResponse.status}`);
      return false;
    }

    const newAppId = createAppResponse.data.application.id;
    console.log(`✅ 测试申请创建成功，ID: ${newAppId}`);

    // 6. 测试用户端获取申请列表
    console.log('\n6. 测试用户端获取申请列表...');
    const userAppsResponse = await axios.get(`${API_BASE_URL}/api/applications`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    if (userAppsResponse.status !== 200) {
      console.log(`❌ 用户端获取申请列表失败: ${userAppsResponse.status}`);
      return false;
    }

    console.log(`✅ 用户端获取申请列表成功，数量: ${userAppsResponse.data.applications.length}`);

    if (userAppsResponse.data.applications.length > 0) {
      const userApp = userAppsResponse.data.applications[0];
      console.log('✅ 用户端API申请列表结构:');
      console.log(`   - actualSampleCount: ${userApp.hasOwnProperty('actualSampleCount') ? '✅' : '❌'} (值: ${userApp.actualSampleCount || 'null'})`);
      console.log(`   - actualProteinCount: ${userApp.hasOwnProperty('actualProteinCount') ? '✅' : '❌'} (值: ${userApp.actualProteinCount || 'null'})`);
      console.log(`   - previewDataFile: ${userApp.hasOwnProperty('previewDataFile') ? '✅' : '❌'} (值: ${userApp.previewDataFile ? 'object' : 'null'})`);
      console.log(`   - completeDataFile: ${userApp.hasOwnProperty('completeDataFile') ? '✅' : '❌'} (值: ${userApp.completeDataFile ? 'object' : 'null'})`);

      // 7. 测试用户端获取申请详情
      console.log('\n7. 测试用户端获取申请详情...');
      const userAppDetailResponse = await axios.get(`${API_BASE_URL}/api/applications/${newAppId}`, {
        headers: { Authorization: `Bearer ${userToken}` }
      });

      if (userAppDetailResponse.status !== 200) {
        console.log(`❌ 用户端获取申请详情失败: ${userAppDetailResponse.status}`);
        return false;
      }

      const userAppDetail = userAppDetailResponse.data.application;
      console.log('✅ 用户端API申请详情结构:');
      console.log(`   - actualSampleCount: ${userAppDetail.hasOwnProperty('actualSampleCount') ? '✅' : '❌'} (值: ${userAppDetail.actualSampleCount || 'null'})`);
      console.log(`   - actualProteinCount: ${userAppDetail.hasOwnProperty('actualProteinCount') ? '✅' : '❌'} (值: ${userAppDetail.actualProteinCount || 'null'})`);
      console.log(`   - previewDataFile: ${userAppDetail.hasOwnProperty('previewDataFile') ? '✅' : '❌'} (值: ${userAppDetail.previewDataFile ? 'object' : 'null'})`);
      console.log(`   - completeDataFile: ${userAppDetail.hasOwnProperty('completeDataFile') ? '✅' : '❌'} (值: ${userAppDetail.completeDataFile ? 'object' : 'null'})`);

      // 检查所有字段是否存在
      const allFieldsPresent = 
        userApp.hasOwnProperty('actualSampleCount') && 
        userApp.hasOwnProperty('actualProteinCount') && 
        userApp.hasOwnProperty('previewDataFile') && 
        userApp.hasOwnProperty('completeDataFile') &&
        userAppDetail.hasOwnProperty('actualSampleCount') && 
        userAppDetail.hasOwnProperty('actualProteinCount') && 
        userAppDetail.hasOwnProperty('previewDataFile') && 
        userAppDetail.hasOwnProperty('completeDataFile');

      if (allFieldsPresent) {
        console.log('\n🎉 所有测试通过！');
        console.log('📋 测试结果:');
        console.log('   ✅ 用户端申请列表API包含所有新字段');
        console.log('   ✅ 用户端申请详情API包含所有新字段');
        console.log('   ✅ API结构正确，前端可以显示报告完成数据');
        console.log('   ✅ 用户可以看到管理员完成的报告信息（当有数据时）');
        return true;
      } else {
        console.log('\n❌ 部分字段缺失');
        return false;
      }
    } else {
      console.log('\n❌ 用户端没有申请数据');
      return false;
    }

  } catch (error) {
    console.log(`\n❌ 测试过程中发生错误: ${error.message}`);
    if (error.response) {
      console.log(`API响应: ${error.response.status} ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testDirectAPI().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testDirectAPI };

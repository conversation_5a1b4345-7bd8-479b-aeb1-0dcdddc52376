<template>
  <div :class="cardClasses">
    <div v-if="$slots.header || title" class="base-card__header">
      <slot name="header">
        <h3 v-if="title" class="base-card__title">{{ title }}</h3>
      </slot>
      <div v-if="$slots.actions" class="base-card__actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <div class="base-card__content">
      <slot></slot>
    </div>
    
    <div v-if="$slots.footer" class="base-card__footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  variant?: 'default' | 'elevated' | 'outlined' | 'flat'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hoverable?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  hoverable: false,
  loading: false
})

const cardClasses = computed(() => [
  'base-card',
  `base-card--${props.variant}`,
  `base-card--padding-${props.padding}`,
  {
    'base-card--hoverable': props.hoverable,
    'base-card--loading': props.loading
  }
])
</script>

<style scoped>
.base-card {
  background: white;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Variants */
.base-card--default {
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.base-card--elevated {
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.base-card--outlined {
  border: 2px solid #e2e8f0;
  box-shadow: none;
}

.base-card--flat {
  border: none;
  box-shadow: none;
  background: #f8fafc;
}

/* Hoverable effect */
.base-card--hoverable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e0;
}

.base-card--flat.base-card--hoverable:hover {
  background: #f1f5f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Padding variants */
.base-card--padding-none .base-card__content {
  padding: 0;
}

.base-card--padding-sm .base-card__content {
  padding: 1.25rem;
}

.base-card--padding-md .base-card__content {
  padding: 1.75rem;
}

.base-card--padding-lg .base-card__content {
  padding: 2.5rem;
}

/* Header */
.base-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
}

.base-card--padding-none .base-card__header {
  padding: 1.5rem 1.5rem 1rem;
  margin-bottom: 0;
}

.base-card--padding-sm .base-card__header {
  padding: 1rem 1rem 0;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
}

.base-card--padding-lg .base-card__header {
  padding: 2rem 2rem 0;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
}

.base-card__title {
  margin: 0;
  color: #2d3748;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.base-card__actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Content */
.base-card__content {
  color: #4a5568;
  line-height: 1.6;
}

/* Footer */
.base-card__footer {
  padding: 0 1.5rem 1.5rem;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  margin-top: 1.5rem;
  padding-top: 1rem;
}

.base-card--padding-none .base-card__footer {
  padding: 1rem 1.5rem 1.5rem;
  margin-top: 0;
}

.base-card--padding-sm .base-card__footer {
  padding: 0 1rem 1rem;
  margin-top: 1rem;
  padding-top: 0.75rem;
}

.base-card--padding-lg .base-card__footer {
  padding: 0 2rem 2rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
}

/* Loading state */
.base-card--loading {
  position: relative;
}

.base-card--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.base-card--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2rem;
  height: 2rem;
  margin: -1rem 0 0 -1rem;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>

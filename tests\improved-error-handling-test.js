const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

// 测试改进的错误处理和移除分析类型字段
async function testImprovedErrorHandling() {
  console.log('🧪 测试改进的错误处理和移除分析类型字段...\n');

  try {
    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const adminLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'fHadmin'
    });

    if (adminLoginResponse.status !== 200) {
      console.log(`❌ 管理员登录失败: ${adminLoginResponse.status}`);
      return false;
    }

    console.log('✅ 管理员登录成功');
    const adminToken = adminLoginResponse.data.token;

    // 2. 创建测试用户
    console.log('\n2. 创建测试用户...');
    const testUserEmail = `testuser${Date.now()}@example.com`;
    const testUserPassword = 'testpass123';

    try {
      await axios.post(`${API_BASE_URL}/api/auth/register`, {
        email: testUserEmail,
        password: testUserPassword,
        firstName: 'Test',
        lastName: 'User'
      });
      console.log('✅ 测试用户创建成功');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('⚠️ 用户可能已存在，继续测试');
      } else {
        throw error;
      }
    }

    // 3. 用户登录
    console.log('\n3. 用户登录...');
    const userLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: testUserEmail,
      password: testUserPassword
    });

    if (userLoginResponse.status !== 200) {
      console.log(`❌ 用户登录失败: ${userLoginResponse.status}`);
      return false;
    }

    console.log('✅ 用户登录成功');
    const userToken = userLoginResponse.data.token;

    // 4. 测试提交不完整的申请（验证详细错误信息）
    console.log('\n4. 测试提交不完整的申请（验证详细错误信息）...');
    
    try {
      await axios.post(`${API_BASE_URL}/api/applications`, {
        // 故意缺少必填字段
        contactInfo: {}, // 缺少phone
        proteinPanel: '', // 空值
        sampleCount: 0, // 无效值
        proteinDataFiles: [] // 空数组
      }, {
        headers: { Authorization: `Bearer ${userToken}` }
      });
      
      console.log('❌ 应该返回验证错误，但请求成功了');
      return false;
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ 正确返回400错误');
        console.log('📋 错误响应结构:');
        console.log(`   - error: ${error.response.data.error}`);
        console.log(`   - message: ${error.response.data.message}`);
        
        if (error.response.data.details) {
          console.log('   - details:');
          error.response.data.details.forEach((detail, index) => {
            console.log(`     ${index + 1}. ${detail.path || detail.param}: ${detail.msg}`);
          });
        }
        
        if (error.response.data.missingFields) {
          console.log('   - missingFields:');
          error.response.data.missingFields.forEach((field, index) => {
            console.log(`     ${index + 1}. ${field.field}: ${field.message}`);
          });
        }
        
        console.log('✅ 错误信息详细且友好');
      } else {
        console.log(`❌ 意外的错误状态: ${error.response?.status}`);
        return false;
      }
    }

    // 5. 测试提交正确的申请（不包含分析类型）
    console.log('\n5. 测试提交正确的申请（不包含分析类型）...');
    
    const validApplication = {
      contactInfo: { phone: '************' },
      proteinPanel: 'olink',
      proteinPanelOption: 'Olink Explore 1536',
      sampleCount: 10,
      proteinDataFiles: [{
        originalName: 'test-data.txt',
        filename: 'test-data.txt',
        path: '/uploads/test-data.txt',
        size: 1024,
        fileType: 'text/plain'
      }],
      groupingFiles: [],
      projectDetails: {
        title: 'Test Project',
        description: 'Test Description',
        objectives: 'Test Objectives'
      },
      sampleTypes: ['plasma'],
      contractNumber: 'TEST001',
      notes: '测试申请，不包含分析类型字段'
    };

    const createAppResponse = await axios.post(`${API_BASE_URL}/api/applications`, validApplication, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    if (createAppResponse.status !== 201) {
      console.log(`❌ 创建申请失败: ${createAppResponse.status}`);
      return false;
    }

    const newAppId = createAppResponse.data.application.id;
    console.log(`✅ 申请创建成功，ID: ${newAppId}`);
    console.log(`   - 默认分析类型: ${createAppResponse.data.application.analysisType}`);

    // 6. 测试管理员完成申请时的错误处理
    console.log('\n6. 测试管理员完成申请时的错误处理...');
    
    try {
      // 故意发送不完整的完成请求
      await axios.post(`${API_BASE_URL}/api/admin/applications/${newAppId}/complete`, {
        // 缺少必需的文件和字段
        reportPrice: 'invalid', // 无效类型
        actualSampleCount: -1 // 无效值
      }, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      
      console.log('❌ 应该返回验证错误，但请求成功了');
      return false;
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ 正确返回400错误');
        console.log('📋 管理员API错误信息:');
        console.log(`   - error: ${error.response.data.error}`);
        console.log(`   - message: ${error.response.data.message}`);
        console.log('✅ 管理员错误信息详细且友好');
      } else {
        console.log(`❌ 意外的错误状态: ${error.response?.status}`);
        return false;
      }
    }

    console.log('\n🎉 所有测试通过！');
    console.log('📋 测试结果总结:');
    console.log('   ✅ 分析类型字段已成功移除');
    console.log('   ✅ 用户端错误信息详细且友好');
    console.log('   ✅ 管理员端错误信息详细且友好');
    console.log('   ✅ 申请可以正常创建（使用默认分析类型）');
    console.log('   ✅ 验证逻辑正确工作');
    console.log('   ✅ 错误响应包含详细的字段信息');

    return true;

  } catch (error) {
    console.log(`\n❌ 测试过程中发生错误: ${error.message}`);
    if (error.response) {
      console.log(`API响应: ${error.response.status} ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testImprovedErrorHandling().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testImprovedErrorHandling };

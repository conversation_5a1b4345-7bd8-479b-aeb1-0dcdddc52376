<template>
  <div class="application-form">
    <!-- 表单头部 -->
    <div class="form-header">
      <h2 class="form-title">{{ $t('submitNewApplication') }}</h2>
      <p class="form-description">{{ $t('applicationFormDescription') }}</p>
    </div>

    <!-- 表单内容 -->
    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 蛋白组Panel选择 -->
      <ProteinPanelSelector
        v-model="formData.proteinPanel"
        :errors="errors"
        @change="handleProteinPanelChange"
      />

      <!-- 文件上传 -->
      <FileUploadSection
        v-model="formData.files"
        :uploading="uploading"
        :errors="errors"
        @upload-files="handleFileUpload"
        @remove-file="handleFileRemove"
      />

      <!-- 联系信息 -->
      <ContactInfoSection
        v-model="formData.contact"
        :errors="errors"
        @change="handleContactChange"
      />

      <!-- 项目详情 -->
      <ProjectDetailsSection
        v-model="formData.project"
        :errors="errors"
        @change="handleProjectChange"
      />

      <!-- 提交按钮 -->
      <div class="form-actions">
        <BaseButton
          type="submit"
          variant="primary"
          size="lg"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          {{ $t('submitApplication') }}
        </BaseButton>
        <BaseButton
          type="button"
          variant="secondary"
          size="lg"
          @click="handleReset"
          :disabled="submitting"
        >
          {{ $t('resetForm') }}
        </BaseButton>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton } from '@/components/ui'
import ProteinPanelSelector from './ProteinPanelSelector.vue'
import FileUploadSection from './FileUploadSection.vue'
import ContactInfoSection from './ContactInfoSection.vue'
import ProjectDetailsSection from './ProjectDetailsSection.vue'

interface ApplicationFormData {
  proteinPanel: {
    proteinPanel: string
    proteinPanelOption: string
    otherPlatformName: string
    sampleCount: number | null
  }
  files: {
    proteinFiles: any[]
    groupFiles: any[]
  }
  contact: {
    phone: string
    contactName: string
    contactOrganization: string
  }
  project: {
    projectName: string
    sampleOrigin: string
    sampleType: string
    otherSampleType: string
    contractNumber: string
    notes: string
  }
}

interface Props {
  submitting?: boolean
  uploading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  submitting: false,
  uploading: false
})

const emit = defineEmits<{
  submit: [data: ApplicationFormData]
  uploadFiles: [files: FileList, type: 'protein' | 'group']
  removeFile: [filename: string, type: 'protein' | 'group']
}>()

const { t } = useI18n()

// 表单数据
const formData = reactive<ApplicationFormData>({
  proteinPanel: {
    proteinPanel: '',
    proteinPanelOption: '',
    otherPlatformName: '',
    sampleCount: null
  },
  files: {
    proteinFiles: [],
    groupFiles: []
  },
  contact: {
    phone: '',
    contactName: '',
    contactOrganization: ''
  },
  project: {
    projectName: '',
    sampleOrigin: '',
    sampleType: '',
    otherSampleType: '',
    contractNumber: '',
    notes: ''
  }
})

// 表单验证错误
const errors = ref<Record<string, string>>({})

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.proteinPanel.proteinPanel &&
    formData.proteinPanel.sampleCount &&
    formData.proteinPanel.sampleCount > 0 &&
    formData.files.proteinFiles.length > 0 &&
    formData.contact.phone &&
    (formData.proteinPanel.proteinPanel !== 'other' || formData.proteinPanel.otherPlatformName) &&
    (formData.project.sampleType !== 'other' || formData.project.otherSampleType)
  )
})

// 事件处理
const handleProteinPanelChange = (data: any) => {
  validateProteinPanel()
}

const handleContactChange = (data: any) => {
  validateContact()
}

const handleProjectChange = (data: any) => {
  validateProject()
}

const handleFileUpload = (files: FileList, type: 'protein' | 'group') => {
  emit('uploadFiles', files, type)
}

const handleFileRemove = (filename: string, type: 'protein' | 'group') => {
  emit('removeFile', filename, type)
}

const handleSubmit = () => {
  if (validateForm()) {
    emit('submit', formData)
  }
}

const handleReset = () => {
  if (confirm(t('confirmResetForm'))) {
    resetForm()
  }
}

// 验证函数
const validateProteinPanel = () => {
  const panelErrors: Record<string, string> = {}
  
  if (!formData.proteinPanel.proteinPanel) {
    panelErrors.proteinPanel = t('proteinPanelRequired')
  }
  
  if (!formData.proteinPanel.sampleCount || formData.proteinPanel.sampleCount <= 0) {
    panelErrors.sampleCount = t('sampleCountRequired')
  }
  
  if (formData.proteinPanel.proteinPanel === 'other' && !formData.proteinPanel.otherPlatformName) {
    panelErrors.otherPlatformName = t('otherPlatformNameRequired')
  }
  
  Object.assign(errors.value, panelErrors)
  return Object.keys(panelErrors).length === 0
}

const validateContact = () => {
  const contactErrors: Record<string, string> = {}
  
  if (!formData.contact.phone) {
    contactErrors.phone = t('contactPhoneRequired')
  } else if (!/^1[3-9]\d{9}$/.test(formData.contact.phone)) {
    contactErrors.phone = t('contactPhoneInvalid')
  }
  
  Object.assign(errors.value, contactErrors)
  return Object.keys(contactErrors).length === 0
}

const validateProject = () => {
  const projectErrors: Record<string, string> = {}
  
  if (formData.project.sampleType === 'other' && !formData.project.otherSampleType) {
    projectErrors.otherSampleType = t('otherSampleTypeRequired')
  }
  
  Object.assign(errors.value, projectErrors)
  return Object.keys(projectErrors).length === 0
}

const validateFiles = () => {
  const fileErrors: Record<string, string> = {}
  
  if (formData.files.proteinFiles.length === 0) {
    fileErrors.proteinFiles = t('proteinFilesRequired')
  }
  
  Object.assign(errors.value, fileErrors)
  return Object.keys(fileErrors).length === 0
}

const validateForm = () => {
  errors.value = {}
  
  const isProteinPanelValid = validateProteinPanel()
  const isContactValid = validateContact()
  const isProjectValid = validateProject()
  const isFilesValid = validateFiles()
  
  return isProteinPanelValid && isContactValid && isProjectValid && isFilesValid
}

const resetForm = () => {
  formData.proteinPanel = {
    proteinPanel: '',
    proteinPanelOption: '',
    otherPlatformName: '',
    sampleCount: null
  }
  formData.files = {
    proteinFiles: [],
    groupFiles: []
  }
  formData.contact = {
    phone: '',
    contactName: '',
    contactOrganization: ''
  }
  formData.project = {
    projectName: '',
    sampleOrigin: '',
    sampleType: '',
    otherSampleType: '',
    contractNumber: '',
    notes: ''
  }
  errors.value = {}
}
</script>

<style scoped>
.application-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.form-header {
  text-align: center;
  margin-bottom: 3rem;
}

.form-title {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-description {
  margin: 0;
  color: #6b7280;
  font-size: 1.125rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 2rem 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-form {
    padding: 1rem;
  }
  
  .form-title {
    font-size: 1.5rem;
  }
  
  .form-description {
    font-size: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>

import { ref, reactive } from 'vue'
import { adminAPI } from '@/services/api'
import type { Application } from '@/types/api'

interface Pagination {
  currentPage: number
  totalPages: number
  totalCount: number
  hasNext: boolean
  hasPrev: boolean
}

interface Filters {
  status: string
  search: string
  sortBy: string
  sortOrder: string
}

interface ApiParams {
  [key: string]: any
}

export function useAdminApplications() {
  const loading = ref(false)
  const applications = ref<Application[]>([])
  const selectedApplication = ref<Application | null>(null)
  const showModal = ref(false)
  
  const pagination = reactive<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false
  })

  const filters = reactive<Filters>({
    status: '',
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const fetchApplications = async (page: number = 1): Promise<void> => {
    try {
      loading.value = true
      const params: ApiParams = {
        page,
        limit: 10,
        ...filters
      }
      
      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })

      const response = await adminAPI.getApplications(params)
      
      if (response.data) {
        applications.value = response.data.applications || []
        if (response.data.pagination) {
          Object.assign(pagination, response.data.pagination)
        }
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateApplicationStatus = async (appId: number, status: string, notes: string = ''): Promise<void> => {
    try {
      await adminAPI.updateApplicationStatus(appId, { status, notes })
      await fetchApplications(pagination.currentPage)
    } catch (error) {
      console.error('Failed to update application status:', error)
      throw error
    }
  }

  const deleteApplication = async (appId: string): Promise<void> => {
    try {
      await adminAPI.deleteApplication(parseInt(appId))
      await fetchApplications(pagination.currentPage)
    } catch (error) {
      console.error('Failed to delete application:', error)
      throw error
    }
  }

  const openModal = (application: Application): void => {
    console.log('🔍 Opening modal with application:', application)
    selectedApplication.value = application
    showModal.value = true
  }

  const closeModal = (): void => {
    selectedApplication.value = null
    showModal.value = false
  }

  const goToPage = (page: number): void => {
    if (page >= 1 && page <= pagination.totalPages) {
      fetchApplications(page)
    }
  }

  const applyFilters = (): void => {
    pagination.currentPage = 1
    fetchApplications(1)
  }

  const resetFilters = (): void => {
    filters.status = ''
    filters.search = ''
    filters.sortBy = 'createdAt'
    filters.sortOrder = 'desc'
    applyFilters()
  }

  return {
    // State
    loading,
    applications,
    selectedApplication,
    showModal,
    pagination,
    filters,
    
    // Actions
    fetchApplications,
    updateApplicationStatus,
    deleteApplication,
    openModal,
    closeModal,
    goToPage,
    applyFilters,
    resetFilters
  }
}

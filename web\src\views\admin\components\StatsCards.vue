<template>
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon">📊</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.totalApplications }}</div>
        <div class="stat-label">总申请数</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">⏳</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.pendingApplications }}</div>
        <div class="stat-label">待处理</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">🔄</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.processingApplications }}</div>
        <div class="stat-label">处理中</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">✅</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.completedApplications }}</div>
        <div class="stat-label">已完成</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">👥</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.totalUsers }}</div>
        <div class="stat-label">总用户数</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">🟢</div>
      <div class="stat-content">
        <div class="stat-number">{{ stats.activeUsers }}</div>
        <div class="stat-label">活跃用户</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">💰</div>
      <div class="stat-content">
        <div class="stat-number">¥{{ formatCurrency(stats.totalRevenue) }}</div>
        <div class="stat-label">总收入</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">📈</div>
      <div class="stat-content">
        <div class="stat-number">¥{{ formatCurrency(stats.monthlyRevenue) }}</div>
        <div class="stat-label">月收入</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

interface Stats {
  totalApplications: number
  pendingApplications: number
  processingApplications: number
  completedApplications: number
  totalUsers: number
  activeUsers: number
  totalRevenue?: number
  monthlyRevenue?: number
}

interface Props {
  stats: Stats
}

defineProps<Props>()

const formatCurrency = (amount?: number) => {
  if (!amount) return '0'
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}
</script>

<style scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: #718096;
  font-weight: 500;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .stat-card {
    padding: 15px;
    gap: 10px;
  }
  
  .stat-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }
  
  .stat-number {
    font-size: 1.4rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
}
</style>

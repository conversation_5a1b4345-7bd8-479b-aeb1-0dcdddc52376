const { sequelize } = require('../models');
require('dotenv').config();

/**
 * 数据库索引添加脚本：为Application表的新字段添加索引
 * 
 * 此脚本将为Application表的新字段添加适当的索引以优化查询性能：
 * - proteinPanel (用于筛选不同的蛋白组平台)
 * - sampleCount (用于按样本数量排序和筛选)
 * - projectName (用于项目名称搜索)
 * - contactName (用于联系人搜索)
 * - sampleType (用于样本类型筛选)
 * - contractNumber (用于合同编号搜索)
 */

async function addApplicationIndexes() {
  const queryInterface = sequelize.getQueryInterface();
  
  try {
    console.log('🔄 开始添加Application表索引...');
    
    // 检查数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 获取现有索引
    const indexes = await queryInterface.showIndex('applications');
    const existingIndexNames = indexes.map(index => index.name);
    
    console.log('📋 现有索引:', existingIndexNames);
    
    const indexesToAdd = [];
    
    // proteinPanel索引 - 用于筛选不同的蛋白组平台
    if (!existingIndexNames.includes('applications_protein_panel')) {
      indexesToAdd.push({
        name: 'applications_protein_panel',
        fields: ['proteinPanel'],
        description: '蛋白组Panel筛选索引'
      });
    }
    
    // sampleCount索引 - 用于按样本数量排序和筛选
    if (!existingIndexNames.includes('applications_sample_count')) {
      indexesToAdd.push({
        name: 'applications_sample_count',
        fields: ['sampleCount'],
        description: '样本数量排序索引'
      });
    }
    
    // projectName索引 - 用于项目名称搜索
    if (!existingIndexNames.includes('applications_project_name')) {
      indexesToAdd.push({
        name: 'applications_project_name',
        fields: ['projectName'],
        description: '项目名称搜索索引'
      });
    }
    
    // contactName索引 - 用于联系人搜索
    if (!existingIndexNames.includes('applications_contact_name')) {
      indexesToAdd.push({
        name: 'applications_contact_name',
        fields: ['contactName'],
        description: '联系人搜索索引'
      });
    }
    
    // sampleType索引 - 用于样本类型筛选
    if (!existingIndexNames.includes('applications_sample_type')) {
      indexesToAdd.push({
        name: 'applications_sample_type',
        fields: ['sampleType'],
        description: '样本类型筛选索引'
      });
    }
    
    // contractNumber索引 - 用于合同编号搜索
    if (!existingIndexNames.includes('applications_contract_number')) {
      indexesToAdd.push({
        name: 'applications_contract_number',
        fields: ['contractNumber'],
        description: '合同编号搜索索引'
      });
    }
    
    // 复合索引：proteinPanel + sampleCount - 用于常见的组合查询
    if (!existingIndexNames.includes('applications_protein_panel_sample_count')) {
      indexesToAdd.push({
        name: 'applications_protein_panel_sample_count',
        fields: ['proteinPanel', 'sampleCount'],
        description: '蛋白组Panel和样本数量复合索引'
      });
    }
    
    // 复合索引：userId + proteinPanel - 用于用户特定的蛋白组查询
    if (!existingIndexNames.includes('applications_user_protein_panel')) {
      indexesToAdd.push({
        name: 'applications_user_protein_panel',
        fields: ['userId', 'proteinPanel'],
        description: '用户蛋白组Panel复合索引'
      });
    }
    
    // 复合索引：status + proteinPanel - 用于管理员筛选
    if (!existingIndexNames.includes('applications_status_protein_panel')) {
      indexesToAdd.push({
        name: 'applications_status_protein_panel',
        fields: ['status', 'proteinPanel'],
        description: '状态蛋白组Panel复合索引'
      });
    }
    
    if (indexesToAdd.length === 0) {
      console.log('ℹ️  所有索引已存在，无需添加新索引');
      return;
    }
    
    console.log(`📝 需要添加 ${indexesToAdd.length} 个新索引:`);
    indexesToAdd.forEach(index => {
      console.log(`   - ${index.name}: ${index.description}`);
    });
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      // 添加索引
      for (const index of indexesToAdd) {
        console.log(`🔧 添加索引: ${index.name}`);
        
        await queryInterface.addIndex('applications', {
          fields: index.fields,
          name: index.name,
          transaction
        });
        
        console.log(`   ✅ ${index.name} 索引添加成功`);
      }
      
      // 提交事务
      await transaction.commit();
      console.log('✅ 索引添加完成！');
      
      // 显示索引统计信息
      console.log('\n📊 索引统计信息:');
      const finalIndexes = await queryInterface.showIndex('applications');
      console.log(`   总索引数量: ${finalIndexes.length}`);
      console.log('   索引列表:');
      finalIndexes.forEach(index => {
        console.log(`     - ${index.name}: [${index.fields.map(f => f.attribute).join(', ')}]`);
      });
      
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error('❌ 索引添加失败:', error.message);
    console.error('详细错误信息:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addApplicationIndexes()
    .then(() => {
      console.log('🎉 索引添加脚本执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 索引添加脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { addApplicationIndexes };

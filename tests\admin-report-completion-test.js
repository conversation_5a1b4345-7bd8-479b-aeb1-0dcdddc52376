const request = require('supertest');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

// 测试管理员报告完成功能的新字段
async function testAdminReportCompletion() {
  console.log('🧪 开始测试管理员报告完成功能的新字段...\n');

  let adminToken;
  let applicationId;

  try {
    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const adminLoginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'fHadmin'
      });

    if (adminLoginResponse.status !== 200) {
      throw new Error(`管理员登录失败: ${adminLoginResponse.status} - ${adminLoginResponse.text}`);
    }

    adminToken = adminLoginResponse.body.token;
    console.log('✅ 管理员登录成功');

    // 2. 创建测试申请
    console.log('\n2. 创建测试申请...');
    const testApplication = {
      contactPhone: '+86-138-0013-8000',
      contactEmail: '<EMAIL>',
      analysisType: 'protein_complex',
      proteinPanel: 'Olink',
      proteinPanelOption: 'Olink-3K',
      sampleCount: 50,
      projectName: '测试项目',
      sampleOrigin: '血清',
      contactName: '张三',
      contactOrganization: '测试机构',
      sampleType: 'serum',
      contractNumber: 'TEST-2024-001',
      notes: '测试申请'
    };

    // 直接在数据库中创建申请以避免文件上传问题
    const createResponse = await request(API_BASE_URL)
      .post('/api/admin/applications/create-test')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(testApplication);

    if (createResponse.status !== 201) {
      // 使用现有的申请进行测试
      console.log('获取现有申请进行测试...');
      const getAppsResponse = await request(API_BASE_URL)
        .get('/api/admin/applications')
        .set('Authorization', `Bearer ${adminToken}`)
        .query({ page: 1, limit: 1 });

      if (getAppsResponse.status === 200 && getAppsResponse.body.applications.length > 0) {
        applicationId = getAppsResponse.body.applications[0].id;
        console.log(`使用现有申请 ID: ${applicationId}`);
      } else {
        throw new Error('无法获取测试申请');
      }
    } else {
      applicationId = createResponse.body.application.id;
    }

    console.log(`✅ 测试申请创建成功，ID: ${applicationId}`);

    // 3. 创建测试文件
    console.log('\n3. 准备测试文件...');
    const testDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // 创建测试文件
    const previewReportPath = path.join(testDir, 'preview-report.pdf');
    const fullReportPath = path.join(testDir, 'full-report.pdf');
    const previewDataPath = path.join(testDir, 'preview-data.csv');
    const completeDataPath = path.join(testDir, 'complete-data.csv');

    fs.writeFileSync(previewReportPath, 'Mock PDF content for preview report');
    fs.writeFileSync(fullReportPath, 'Mock PDF content for full report');
    fs.writeFileSync(previewDataPath, 'protein_id,expression_level\nP12345,2.5\nP67890,1.8');
    fs.writeFileSync(completeDataPath, 'protein_id,expression_level,p_value,fold_change\nP12345,2.5,0.001,2.1\nP67890,1.8,0.005,1.6\nP11111,3.2,0.0001,3.5');

    console.log('✅ 测试文件创建完成');

    // 4. 测试管理员完成报告（包含新字段）
    console.log('\n4. 测试管理员完成报告（包含新字段）...');
    const completeResponse = await request(API_BASE_URL)
      .post(`/api/admin/applications/${applicationId}/complete`)
      .set('Authorization', `Bearer ${adminToken}`)
      .attach('previewReport', previewReportPath)
      .attach('fullReport', fullReportPath)
      .attach('previewDataFile', previewDataPath)
      .attach('completeDataFile', completeDataPath)
      .field('actualSampleCount', '48')
      .field('actualProteinCount', '1520')
      .field('reportPrice', '299.99')
      .field('paymentStatus', 'unpaid')
      .field('notes', '报告完成，包含新的数据文件字段');

    if (completeResponse.status !== 200) {
      throw new Error(`完成报告失败: ${completeResponse.status} - ${JSON.stringify(completeResponse.body)}`);
    }

    console.log('✅ 报告完成成功');
    console.log('📊 返回的申请数据:', JSON.stringify(completeResponse.body.application, null, 2));

    // 5. 验证新字段
    console.log('\n5. 验证新字段是否正确保存...');
    const application = completeResponse.body.application;
    
    const validations = [
      { field: 'actualSampleCount', expected: 48, actual: application.actualSampleCount },
      { field: 'actualProteinCount', expected: 1520, actual: application.actualProteinCount },
      { field: 'previewDataFile', expected: 'object', actual: typeof application.previewDataFile },
      { field: 'completeDataFile', expected: 'object', actual: typeof application.completeDataFile },
      { field: 'status', expected: 'completed', actual: application.status }
    ];

    let allValid = true;
    validations.forEach(({ field, expected, actual }) => {
      if (actual === expected || (expected === 'object' && actual === 'object' && application[field] !== null)) {
        console.log(`✅ ${field}: ${actual} (符合预期)`);
      } else {
        console.log(`❌ ${field}: 期望 ${expected}, 实际 ${actual}`);
        allValid = false;
      }
    });

    // 6. 测试用户端能否看到新字段
    console.log('\n6. 测试用户端获取申请详情...');
    const userResponse = await request(API_BASE_URL)
      .get(`/api/applications/${applicationId}`)
      .set('Authorization', `Bearer ${adminToken}`); // 使用管理员token模拟用户访问

    if (userResponse.status === 200) {
      const userApp = userResponse.body.application;
      console.log('✅ 用户端可以获取申请详情');
      console.log(`📊 实际样本数量: ${userApp.actualSampleCount}`);
      console.log(`📊 实际蛋白质数量: ${userApp.actualProteinCount}`);
      console.log(`📁 预览数据文件: ${userApp.previewDataFile ? '已提供' : '未提供'}`);
      console.log(`📁 完整数据文件: ${userApp.completeDataFile ? '已提供' : '未提供'}`);
    } else {
      console.log(`⚠️ 用户端获取申请详情失败: ${userResponse.status}`);
    }

    // 7. 清理测试文件
    console.log('\n7. 清理测试文件...');
    [previewReportPath, fullReportPath, previewDataPath, completeDataPath].forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    });
    if (fs.existsSync(testDir)) {
      fs.rmdirSync(testDir);
    }
    console.log('✅ 测试文件清理完成');

    if (allValid) {
      console.log('\n🎉 所有测试通过！管理员报告完成功能的新字段工作正常。');
      return true;
    } else {
      console.log('\n❌ 部分测试失败，请检查实现。');
      return false;
    }

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应详情:', error.response.data);
    }
    return false;
  }
}

// 运行测试
if (require.main === module) {
  testAdminReportCompletion()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAdminReportCompletion };

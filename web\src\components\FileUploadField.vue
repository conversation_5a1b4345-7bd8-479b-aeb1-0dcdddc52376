<template>
  <div class="file-upload-field">
    <label :for="fieldId" class="file-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>
    
    <div class="file-input-wrapper">
      <input
        :id="fieldId"
        ref="fileInputRef"
        type="file"
        :accept="accept"
        :multiple="multiple"
        class="file-input"
        @change="handleFileChange"
      />
      
      <div v-if="selectedFile" class="file-info">
        <span class="file-name">{{ selectedFile.name }}</span>
        <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
        <button type="button" @click="clearFile" class="clear-btn">×</button>
      </div>
      
      <div v-if="!selectedFile" class="file-placeholder">
        {{ placeholder || '选择文件...' }}
      </div>
    </div>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  label: string
  required?: boolean
  accept?: string
  multiple?: boolean
  placeholder?: string
  modelValue?: File | null
  error?: string
}

interface Emits {
  (e: 'update:modelValue', value: File | null): void
  (e: 'change', file: File | null): void
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  multiple: false,
  accept: '*/*'
})

const emit = defineEmits<Emits>()

const fileInputRef = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(props.modelValue || null)

const fieldId = computed(() => `file-${Math.random().toString(36).substr(2, 9)}`)

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  selectedFile.value = newValue
})

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0] || null
  
  selectedFile.value = file
  emit('update:modelValue', file)
  emit('change', file)
}

const clearFile = () => {
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
  selectedFile.value = null
  emit('update:modelValue', null)
  emit('change', null)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Expose the file input ref for external access
defineExpose({
  fileInputRef,
  selectedFile,
  clearFile
})
</script>

<style scoped>
.file-upload-field {
  margin-bottom: 20px;
}

.file-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.required {
  color: #dc3545;
  margin-left: 4px;
}

.file-input-wrapper {
  position: relative;
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
  background: #f8f9fa;
}

.file-input-wrapper:hover {
  border-color: #007bff;
  background: #e3f2fd;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #495057;
}

.file-name {
  font-weight: 500;
  color: #007bff;
}

.file-size {
  color: #6c757d;
  font-size: 12px;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.file-placeholder {
  color: #6c757d;
  font-style: italic;
}

.error-message {
  margin-top: 8px;
  color: #dc3545;
  font-size: 12px;
}

/* File input wrapper states */
.file-input-wrapper.has-file {
  border-color: #28a745;
  background: #d4edda;
}

.file-input-wrapper.has-error {
  border-color: #dc3545;
  background: #f8d7da;
}
</style>

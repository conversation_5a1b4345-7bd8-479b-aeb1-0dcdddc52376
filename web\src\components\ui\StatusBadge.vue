<template>
  <span :class="badgeClasses">
    <span v-if="icon" class="status-badge__icon">{{ icon }}</span>
    <slot>{{ text }}</slot>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'paid' | 'unpaid' | 'free'
  variant?: 'default' | 'dot' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  text?: string
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  status: 'pending',
  variant: 'default',
  size: 'md'
})

const badgeClasses = computed(() => [
  'status-badge',
  `status-badge--${props.status}`,
  `status-badge--${props.variant}`,
  `status-badge--${props.size}`
])

// 默认图标映射
const defaultIcons = {
  pending: '⏳',
  processing: '🔄',
  completed: '✅',
  failed: '❌',
  cancelled: '🚫',
  paid: '💰',
  unpaid: '💳',
  free: '🆓'
}

const icon = computed(() => props.icon || (props.variant === 'dot' ? '' : defaultIcons[props.status]))
</script>

<style scoped>
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  border-radius: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  transition: all 0.2s ease;
}

/* Sizes */
.status-badge--sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  min-height: 1.25rem;
}

.status-badge--md {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  min-height: 1.5rem;
}

.status-badge--lg {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  min-height: 2rem;
}

/* Status Colors - Default Variant */
.status-badge--default.status-badge--pending {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.status-badge--default.status-badge--processing {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-badge--default.status-badge--completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.status-badge--default.status-badge--failed {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.status-badge--default.status-badge--cancelled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.status-badge--default.status-badge--paid {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-badge--default.status-badge--unpaid {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.status-badge--default.status-badge--free {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* Outline Variant */
.status-badge--outline {
  background: transparent;
  border: 2px solid;
}

.status-badge--outline.status-badge--pending {
  border-color: #f59e0b;
  color: #f59e0b;
  background: rgba(251, 191, 36, 0.1);
}

.status-badge--outline.status-badge--processing {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.status-badge--outline.status-badge--completed {
  border-color: #10b981;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.status-badge--outline.status-badge--failed {
  border-color: #ef4444;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.status-badge--outline.status-badge--cancelled {
  border-color: #6b7280;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.status-badge--outline.status-badge--paid {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.status-badge--outline.status-badge--unpaid {
  border-color: #f59e0b;
  color: #f59e0b;
  background: rgba(251, 191, 36, 0.1);
}

.status-badge--outline.status-badge--free {
  border-color: #10b981;
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

/* Dot Variant */
.status-badge--dot {
  background: rgba(0, 0, 0, 0.05);
  color: #4a5568;
  position: relative;
  padding-left: 1.5rem;
}

.status-badge--dot::before {
  content: '';
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-badge--dot.status-badge--pending::before {
  background: #f59e0b;
}

.status-badge--dot.status-badge--processing::before {
  background: #3b82f6;
  animation: pulse 2s infinite;
}

.status-badge--dot.status-badge--completed::before {
  background: #10b981;
}

.status-badge--dot.status-badge--failed::before {
  background: #ef4444;
}

.status-badge--dot.status-badge--cancelled::before {
  background: #6b7280;
}

.status-badge--dot.status-badge--paid::before {
  background: #3b82f6;
}

.status-badge--dot.status-badge--unpaid::before {
  background: #f59e0b;
}

.status-badge--dot.status-badge--free::before {
  background: #10b981;
}

.status-badge__icon {
  font-size: 0.875em;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>

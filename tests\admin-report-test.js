/**
 * Admin Report Completion Test
 * 
 * This test focuses on testing the admin report completion functionality
 * with the new fields (preview data file, complete data file, actual sample count, actual protein count).
 */

const request = require('supertest');
const fs = require('fs');
const path = require('path');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const TEST_FILES_DIR = path.join(__dirname, 'test-files');

// Test data - using existing admin account
const testAdmin = {
  email: '<EMAIL>',
  password: 'fHadmin'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`${colors.cyan}ℹ️  ${message}${colors.reset}`);
}

// Helper functions
function createTestFile(filename, content) {
  if (!fs.existsSync(TEST_FILES_DIR)) {
    fs.mkdirSync(TEST_FILES_DIR, { recursive: true });
  }
  const filePath = path.join(TEST_FILES_DIR, filename);
  fs.writeFileSync(filePath, content);
  return filePath;
}

function cleanupTestFiles() {
  if (fs.existsSync(TEST_FILES_DIR)) {
    fs.rmSync(TEST_FILES_DIR, { recursive: true, force: true });
  }
}

// Test functions
async function testAdminLogin() {
  logInfo('Testing admin login...');
  
  try {
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send(testAdmin);
    
    if (loginResponse.status !== 200) {
      throw new Error(`Admin login failed: ${loginResponse.status} - ${loginResponse.text}`);
    }

    const adminToken = loginResponse.body.token;
    logSuccess('Admin login completed');
    return adminToken;
    
  } catch (error) {
    logError(`Admin login failed: ${error.message}`);
    throw error;
  }
}

async function createTestUser() {
  logInfo('Creating test user for application submission...');

  const testUser = {
    email: `test-user-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    confirmPassword: 'TestPassword123!',
    firstName: 'Test',
    lastName: 'User'
  };

  try {
    // Register user
    const registerResponse = await request(API_BASE_URL)
      .post('/api/auth/register')
      .send(testUser);

    if (registerResponse.status !== 201) {
      throw new Error(`User registration failed: ${registerResponse.status} - ${registerResponse.text}`);
    }

    // Mark user as verified (bypass email verification for testing)
    const { User } = require('../apiserver/models');
    await User.update(
      { isEmailVerified: true },
      { where: { email: testUser.email } }
    );

    // Login user
    const loginResponse = await request(API_BASE_URL)
      .post('/api/auth/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });

    if (loginResponse.status !== 200) {
      throw new Error(`User login failed: ${loginResponse.status} - ${loginResponse.text}`);
    }

    logSuccess('Test user created and logged in');
    return {
      user: testUser,
      token: loginResponse.body.token
    };

  } catch (error) {
    logError(`Failed to create test user: ${error.message}`);
    throw error;
  }
}

async function createTestApplication(userToken) {
  logInfo('Creating test application directly in database...');

  try {
    // Get user ID from token
    const userResponse = await request(API_BASE_URL)
      .get('/api/auth/me')
      .set('Authorization', `Bearer ${userToken}`);

    if (userResponse.status !== 200) {
      throw new Error('Failed to get user profile');
    }

    const userId = userResponse.body.user.id;

    // Create application directly in database
    const { Application } = require('../apiserver/models');

    const application = await Application.create({
      userId: userId,
      contactPhone: '13800138000',
      contactEmail: userResponse.body.user.email,
      proteinPanel: 'olink',
      proteinPanelOption: 'Inflammation',
      sampleCount: 50,
      projectName: 'Test Project for Report Completion',
      sampleOrigin: 'Test Lab',
      contactName: 'Test User',
      contactOrganization: 'Test Organization',
      sampleType: 'human',
      contractNumber: 'TEST-001',
      proteinDataFiles: [{
        originalName: 'test-protein-data.csv',
        filename: `test-protein-data-${Date.now()}.csv`,
        path: '/test/path/test-protein-data.csv',
        size: 1024,
        mimetype: 'text/csv',
        fileType: 'csv',
        uploadedAt: new Date(),
        isProcessed: false
      }],
      sampleGroupFiles: [{
        originalName: 'test-grouping.csv',
        filename: `test-grouping-${Date.now()}.csv`,
        path: '/test/path/test-grouping.csv',
        size: 512,
        mimetype: 'text/csv',
        fileType: 'csv',
        uploadedAt: new Date(),
        isProcessed: false
      }],
      files: [], // Keep for compatibility
      analysisType: 'protein_complex',
      parameters: {
        algorithm: 'alphafold',
        confidence: 0.7
      },
      status: 'pending',
      progressPercentage: 0,
      currentStep: 'Queued',
      estimatedCost: 299.99,
      notes: 'Test application for report completion functionality'
    });

    logSuccess(`Test application created directly in database with ID: ${application.id}`);
    return application.id;

  } catch (error) {
    logError(`Failed to create test application: ${error.message}`);
    throw error;
  }
}

async function testGetApplications(adminToken) {
  logInfo('Testing admin get applications...');

  try {
    const response = await request(API_BASE_URL)
      .get('/api/admin/applications')
      .set('Authorization', `Bearer ${adminToken}`);

    if (response.status !== 200) {
      throw new Error(`Failed to get applications: ${response.status} - ${response.text}`);
    }

    const responseData = response.body;
    const applications = responseData.applications || responseData.data || responseData;

    logSuccess(`Found ${Array.isArray(applications) ? applications.length : 'unknown number of'} applications`);

    if (!Array.isArray(applications) || applications.length === 0) {
      logInfo('No applications found. Will create a test application...');
      return null;
    }

    // Find an application that hasn't been completed yet
    const pendingApp = applications.find(app => app.status === 'pending' || app.status === 'processing');
    if (pendingApp) {
      logInfo(`Using pending application ID: ${pendingApp.id}`);
      return pendingApp.id;
    }

    // Use the first application
    logInfo(`Using first application ID: ${applications[0].id}`);
    return applications[0].id;

  } catch (error) {
    logError(`Failed to get applications: ${error.message}`);
    throw error;
  }
}

async function testReportCompletion(adminToken, applicationId, previewDataFile, completeDataFile) {
  logInfo('Testing report completion with new fields...');

  try {
    // First, let's check the current application status
    const getResponse = await request(API_BASE_URL)
      .get(`/api/admin/applications/${applicationId}`)
      .set('Authorization', `Bearer ${adminToken}`);

    if (getResponse.status !== 200) {
      throw new Error(`Failed to get application details: ${getResponse.status} - ${getResponse.text}`);
    }

    logInfo(`Current application status: ${getResponse.body.application ? getResponse.body.application.status : getResponse.body.status}`);

    // Create required report files
    const previewReportFile = createTestFile('preview-report.txt', 'Mock preview report content');
    const fullReportFile = createTestFile('full-report.txt', 'Mock full report content');

    // Test the report completion endpoint
    const response = await request(API_BASE_URL)
      .post(`/api/admin/applications/${applicationId}/complete`)
      .set('Authorization', `Bearer ${adminToken}`)
      .field('reportPrice', '299.99')
      .field('paymentStatus', 'unpaid')
      .field('actualSampleCount', '48')
      .field('actualProteinCount', '1520')
      .field('notes', 'Admin report completion test')
      .attach('previewReport', previewReportFile)
      .attach('fullReport', fullReportFile)
      .attach('previewDataFile', previewDataFile)
      .attach('completeDataFile', completeDataFile);

    if (response.status !== 200) {
      throw new Error(`Report completion failed: ${response.status} - ${response.text}`);
    }

    logSuccess('Report completion with new fields completed');
    logInfo(`Updated application status: ${response.body.application.status}`);
    logInfo(`Actual Sample Count: ${response.body.application.actualSampleCount}`);
    logInfo(`Actual Protein Count: ${response.body.application.actualProteinCount}`);

    return response.body.application;

  } catch (error) {
    logError(`Report completion failed: ${error.message}`);
    throw error;
  }
}

async function testGetCompletedApplication(adminToken, applicationId) {
  logInfo('Testing retrieval of completed application...');
  
  try {
    const response = await request(API_BASE_URL)
      .get(`/api/admin/applications/${applicationId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    if (response.status !== 200) {
      throw new Error(`Failed to get completed application: ${response.status} - ${response.text}`);
    }

    const application = response.body.application || response.body;
    
    // Log all application fields for debugging
    logInfo(`Application fields: ${JSON.stringify(application, null, 2)}`);

    // Verify new fields are present
    if (application.actualSampleCount === null || application.actualSampleCount === undefined) {
      logError(`actualSampleCount is: ${application.actualSampleCount}`);
      throw new Error('actualSampleCount field is missing or null');
    }
    if (application.actualProteinCount === null || application.actualProteinCount === undefined) {
      logError(`actualProteinCount is: ${application.actualProteinCount}`);
      throw new Error('actualProteinCount field is missing or null');
    }
    
    logSuccess('Completed application retrieved successfully');
    logInfo(`Application ID: ${application.id}`);
    logInfo(`Status: ${application.status}`);
    logInfo(`Actual Sample Count: ${application.actualSampleCount}`);
    logInfo(`Actual Protein Count: ${application.actualProteinCount}`);
    logInfo(`Report Price: ${application.reportPrice}`);
    
    return application;
    
  } catch (error) {
    logError(`Failed to get completed application: ${error.message}`);
    throw error;
  }
}

// Main test runner
async function runAdminReportTest() {
  log(`${colors.bright}🧪 Admin Report Completion Test${colors.reset}`);
  log('');

  let adminToken, applicationId, userToken;
  let previewDataFile, completeDataFile;

  try {
    // Setup test files
    logInfo('Setting up test files...');
    previewDataFile = createTestFile('preview-data.txt', 'sample_id,protein_1,protein_2\nS001,100,200\nS002,150,250');
    completeDataFile = createTestFile('complete-data.txt', 'sample_id,protein_1,protein_2,protein_3\nS001,100,200,300\nS002,150,250,350');
    logSuccess('Test files created');

    // Run tests
    adminToken = await testAdminLogin();
    applicationId = await testGetApplications(adminToken);

    if (!applicationId) {
      logInfo('Creating test application for testing...');
      const testUserData = await createTestUser();
      userToken = testUserData.token;
      applicationId = await createTestApplication(userToken);
    }

    await testReportCompletion(adminToken, applicationId, previewDataFile, completeDataFile);
    await testGetCompletedApplication(adminToken, applicationId);

    // Success
    log('');
    log(`${colors.green}${colors.bright}🎉 Admin report completion test passed successfully!${colors.reset}`);
    log('');
    log('📊 Test Results:');
    log('   ✅ Admin login');
    log('   ✅ Get applications list');
    log('   ✅ Test user creation (if needed)');
    log('   ✅ Test application creation (if needed)');
    log('   ✅ Report completion with new fields');
    log('   ✅ Retrieve completed application with new data');
    log('');

  } catch (error) {
    logError(`Admin report test failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Cleanup
    logInfo('Cleaning up test files...');
    cleanupTestFiles();
    logSuccess('Cleanup completed');
  }
}

// Run the test
if (require.main === module) {
  runAdminReportTest().catch((error) => {
    logError(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runAdminReportTest
};

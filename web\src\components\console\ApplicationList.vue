<template>
  <div class="application-list">
    <!-- 加载状态 -->
    <LoadingState
      v-if="loading"
      type="skeleton"
      size="lg"
      :text="$t('loadingText')"
    />

    <!-- 空状态 -->
    <div v-else-if="applications.length === 0" class="empty-state">
      <div class="empty-content">
        <div class="empty-icon">📋</div>
        <h3 class="empty-title">{{ $t("noApplicationsYet") }}</h3>
        <p class="empty-description">
          {{ $t("noApplicationsDescription") }}
        </p>
        <BaseButton
          variant="primary"
          size="lg"
          @click="createNewApplication"
        >
          {{ $t("submitNewApplicationNow") }}
        </BaseButton>
      </div>
    </div>

    <!-- 应用列表 -->
    <div v-else class="applications-container">
      <!-- 列表头部 -->
      <div class="list-header">
        <h2 class="list-title">{{ $t("myAnalysisApplications") }}</h2>
        <div class="list-actions">
          <BaseButton
            variant="outline"
            size="sm"
            @click="refreshApplications"
            :loading="refreshing"
          >
            🔄 {{ $t("refresh") }}
          </BaseButton>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="list-filters">
        <div class="filter-group">
          <FormField
            v-model="statusFilter"
            type="select"
            :placeholder="$t('allStatuses')"
            :options="statusOptions"
            @change="applyFilters"
          />
        </div>
        <div class="filter-group">
          <FormField
            v-model="sortBy"
            type="select"
            :placeholder="$t('sortBy')"
            :options="sortOptions"
            @change="applySorting"
          />
        </div>
      </div>

      <!-- 应用项列表 -->
      <div class="applications-grid">
        <ApplicationItem
          v-for="application in filteredApplications"
          :key="application.id"
          :application="application"
          @download-report="handleDownloadReport"
          @download-data-file="handleDownloadDataFile"
          @initiate-payment="handleInitiatePayment"
          @view-report="handleViewReport"
          @cancel-application="handleCancelApplication"
        />
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <BaseButton
          variant="outline"
          size="sm"
          :disabled="currentPage === 1"
          @click="goToPage(currentPage - 1)"
        >
          ← {{ $t("previous") }}
        </BaseButton>
        
        <span class="page-info">
          {{ $t("pageInfo", { current: currentPage, total: totalPages }) }}
        </span>
        
        <BaseButton
          variant="outline"
          size="sm"
          :disabled="currentPage === totalPages"
          @click="goToPage(currentPage + 1)"
        >
          {{ $t("next") }} →
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, FormField, LoadingState } from '@/components/ui'
import ApplicationItem from './ApplicationItem.vue'
import type { Application } from '@/types/api'

interface Props {
  applications: Application[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  downloadReport: [applicationId: number, type: 'preview' | 'full']
  downloadDataFile: [applicationId: number, type: 'preview' | 'complete']
  initiatePayment: [applicationId: number, price: number]
  viewReport: [applicationId: string]
  cancelApplication: [applicationId: number]
  createNewApplication: []
  refreshApplications: []
}>()

const { t } = useI18n()

// 筛选和排序状态
const statusFilter = ref('')
const sortBy = ref('createdAt-desc')
const currentPage = ref(1)
const pageSize = 10
const refreshing = ref(false)

// 筛选选项
const statusOptions = [
  { label: t('allStatuses'), value: '' },
  { label: t('pending'), value: 'pending' },
  { label: t('processing'), value: 'processing' },
  { label: t('completed'), value: 'completed' },
  { label: t('failed'), value: 'failed' },
  { label: t('cancelled'), value: 'cancelled' }
]

// 排序选项
const sortOptions = [
  { label: t('newestFirst'), value: 'createdAt-desc' },
  { label: t('oldestFirst'), value: 'createdAt-asc' },
  { label: t('statusAsc'), value: 'status-asc' },
  { label: t('statusDesc'), value: 'status-desc' }
]

// 筛选后的应用列表
const filteredApplications = computed(() => {
  let filtered = [...props.applications]

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(app => app.status === statusFilter.value)
  }

  // 排序
  const [field, order] = sortBy.value.split('-')
  filtered.sort((a, b) => {
    let aValue = a[field as keyof Application]
    let bValue = b[field as keyof Application]

    if (field === 'createdAt') {
      aValue = new Date(aValue as string).getTime()
      bValue = new Date(bValue as string).getTime()
    }

    if (order === 'desc') {
      return aValue > bValue ? -1 : 1
    } else {
      return aValue < bValue ? -1 : 1
    }
  })

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filtered.slice(start, end)
})

// 总页数
const totalPages = computed(() => {
  const totalItems = statusFilter.value 
    ? props.applications.filter(app => app.status === statusFilter.value).length
    : props.applications.length
  return Math.ceil(totalItems / pageSize)
})

// 事件处理
const handleDownloadReport = (applicationId: number, type: 'preview' | 'full') => {
  emit('downloadReport', applicationId, type)
}

const handleDownloadDataFile = (applicationId: number, type: 'preview' | 'complete') => {
  emit('downloadDataFile', applicationId, type)
}

const handleInitiatePayment = (applicationId: number, price: number) => {
  emit('initiatePayment', applicationId, price)
}

const handleViewReport = (applicationId: string) => {
  emit('viewReport', applicationId)
}

const handleCancelApplication = (applicationId: number) => {
  emit('cancelApplication', applicationId)
}

const createNewApplication = () => {
  emit('createNewApplication')
}

const refreshApplications = async () => {
  refreshing.value = true
  try {
    emit('refreshApplications')
  } finally {
    setTimeout(() => {
      refreshing.value = false
    }, 1000)
  }
}

const applyFilters = () => {
  currentPage.value = 1
}

const applySorting = () => {
  currentPage.value = 1
}

const goToPage = (page: number) => {
  currentPage.value = page
}
</script>

<style scoped>
.application-list {
  width: 100%;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-description {
  margin: 0 0 2rem 0;
  color: #6b7280;
  line-height: 1.6;
}

.applications-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.list-title {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.list-actions {
  display: flex;
  gap: 0.5rem;
}

.list-filters {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  min-width: 200px;
}

.applications-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0;
}

.page-info {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .list-filters {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .filter-group {
    min-width: auto;
  }
  
  .pagination {
    flex-direction: column;
    gap: 0.75rem;
  }
}
</style>

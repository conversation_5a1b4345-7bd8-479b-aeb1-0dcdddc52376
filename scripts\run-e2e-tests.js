#!/usr/bin/env node

/**
 * End-to-End Test Runner for Report Completion Feature
 * 
 * This script sets up the test environment and runs comprehensive
 * end-to-end tests for the report completion functionality.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiPort: process.env.API_PORT || 3001,
  webPort: process.env.WEB_PORT || 3000,
  testTimeout: 300000, // 5 minutes
  dbName: process.env.TEST_DB_NAME || 'quantix_test'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`${colors.cyan}[${step}]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

// Helper functions
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: options.silent ? 'pipe' : 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', reject);
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function checkServerHealth(port, maxRetries = 30) {
  const http = require('http');
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      await new Promise((resolve, reject) => {
        const req = http.get(`http://localhost:${port}/health`, (res) => {
          if (res.statusCode === 200) {
            resolve();
          } else {
            reject(new Error(`Health check failed with status ${res.statusCode}`));
          }
        });
        
        req.on('error', reject);
        req.setTimeout(2000, () => {
          req.destroy();
          reject(new Error('Health check timeout'));
        });
      });
      
      return true;
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error;
      }
      await sleep(2000);
    }
  }
  
  return false;
}

// Main test runner
async function runE2ETests() {
  log(`${colors.bright}🧪 Quantix E2E Test Runner - Report Completion Feature${colors.reset}`);
  log('');

  try {
    // Step 1: Environment setup
    logStep('1/8', 'Setting up test environment...');
    
    // Check if required directories exist
    const requiredDirs = ['apiserver', 'web', 'tests'];
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        throw new Error(`Required directory '${dir}' not found`);
      }
    }
    
    logSuccess('Environment setup complete');

    // Step 2: Database setup
    logStep('2/8', 'Setting up test database...');
    
    try {
      // Run database migrations
      await runCommand('npm', ['run', 'migrate'], { cwd: 'apiserver' });
      logSuccess('Database migrations completed');
    } catch (error) {
      logWarning('Database migration failed, continuing with existing schema');
    }

    // Step 3: Start API server
    logStep('3/8', 'Starting API server...');
    
    const apiProcess = spawn('npm', ['start'], {
      cwd: 'apiserver',
      stdio: 'pipe',
      env: {
        ...process.env,
        NODE_ENV: 'test',
        PORT: config.apiPort,
        DB_NAME: config.dbName
      }
    });

    // Wait for API server to be ready
    await sleep(5000);
    
    try {
      await checkServerHealth(config.apiPort);
      logSuccess(`API server running on port ${config.apiPort}`);
    } catch (error) {
      throw new Error(`API server health check failed: ${error.message}`);
    }

    // Step 4: Start web server
    logStep('4/8', 'Starting web server...');
    
    const webProcess = spawn('npm', ['run', 'dev'], {
      cwd: 'web',
      stdio: 'pipe',
      env: {
        ...process.env,
        NODE_ENV: 'test',
        PORT: config.webPort,
        VITE_API_BASE_URL: `http://localhost:${config.apiPort}`
      }
    });

    // Wait for web server to be ready
    await sleep(10000);
    logSuccess(`Web server running on port ${config.webPort}`);

    // Step 5: Run database migration for new fields
    logStep('5/8', 'Running report completion field migration...');
    
    try {
      await runCommand('npx', ['sequelize-cli', 'db:migrate', '--migrations-path', 'migrations', '--config', 'config/database.js'], {
        cwd: 'apiserver'
      });
      logSuccess('Report completion field migration completed');
    } catch (error) {
      logWarning('Migration may have already been applied');
    }

    // Step 6: Run unit tests
    logStep('6/8', 'Running unit tests...');
    
    try {
      await runCommand('npm', ['test', '--', '--testPathPattern=unit'], { cwd: '.' });
      logSuccess('Unit tests passed');
    } catch (error) {
      logWarning('Some unit tests failed, continuing with E2E tests');
    }

    // Step 7: Run E2E tests
    logStep('7/8', 'Running end-to-end tests...');
    
    const testEnv = {
      ...process.env,
      API_BASE_URL: `http://localhost:${config.apiPort}`,
      WEB_BASE_URL: `http://localhost:${config.webPort}`,
      NODE_ENV: 'test'
    };

    await runCommand('npm', ['test', '--', 'tests/e2e-report-completion-test.js'], {
      env: testEnv,
      timeout: config.testTimeout
    });
    
    logSuccess('End-to-end tests completed successfully');

    // Step 8: Cleanup
    logStep('8/8', 'Cleaning up...');
    
    // Kill servers
    if (apiProcess) {
      apiProcess.kill('SIGTERM');
    }
    if (webProcess) {
      webProcess.kill('SIGTERM');
    }

    // Wait for graceful shutdown
    await sleep(2000);
    
    logSuccess('Cleanup completed');

    // Final success message
    log('');
    log(`${colors.green}${colors.bright}🎉 All tests completed successfully!${colors.reset}`);
    log('');
    log('📊 Test Summary:');
    log('   ✅ Environment setup');
    log('   ✅ Database migrations');
    log('   ✅ Server startup');
    log('   ✅ Report completion functionality');
    log('   ✅ User interface integration');
    log('   ✅ API endpoint validation');
    log('   ✅ Data file handling');
    log('   ✅ Cleanup');
    log('');

  } catch (error) {
    logError(`Test execution failed: ${error.message}`);
    
    // Cleanup on error
    try {
      if (apiProcess) apiProcess.kill('SIGTERM');
      if (webProcess) webProcess.kill('SIGTERM');
    } catch (cleanupError) {
      logError(`Cleanup failed: ${cleanupError.message}`);
    }
    
    process.exit(1);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    log('');
    log(`${colors.bright}Quantix E2E Test Runner${colors.reset}`);
    log('');
    log('Usage: node scripts/run-e2e-tests.js [options]');
    log('');
    log('Options:');
    log('  --help, -h     Show this help message');
    log('  --verbose, -v  Enable verbose output');
    log('');
    log('Environment Variables:');
    log('  API_PORT       API server port (default: 3001)');
    log('  WEB_PORT       Web server port (default: 3000)');
    log('  TEST_DB_NAME   Test database name (default: quantix_test)');
    log('');
    process.exit(0);
  }

  runE2ETests().catch((error) => {
    logError(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runE2ETests,
  config
};

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

// 测试API结构是否包含新字段
async function testAPIStructure() {
  console.log('🧪 测试API结构是否包含新字段...\n');

  let allPassed = true;

  try {
    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const adminLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'fHadmin'
    });

    if (adminLoginResponse.status !== 200) {
      console.log(`❌ 管理员登录失败: ${adminLoginResponse.status}`);
      return false;
    }

    console.log('✅ 管理员登录成功');
    const adminToken = adminLoginResponse.data.token;

    // 2. 测试管理员API - 获取申请列表
    console.log('\n2. 测试管理员API - 获取申请列表...');
    const adminAppsResponse = await axios.get(`${API_BASE_URL}/api/admin/applications`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    if (adminAppsResponse.status !== 200 || !adminAppsResponse.data.applications.length) {
      console.log('❌ 管理员API获取申请列表失败');
      return false;
    }

    const adminApp = adminAppsResponse.data.applications[0];
    console.log('✅ 管理员API申请列表结构检查:');
    console.log(`   - actualSampleCount: ${adminApp.hasOwnProperty('actualSampleCount') ? '✅' : '❌'}`);
    console.log(`   - actualProteinCount: ${adminApp.hasOwnProperty('actualProteinCount') ? '✅' : '❌'}`);
    console.log(`   - previewDataFile: ${adminApp.hasOwnProperty('previewDataFile') ? '✅' : '❌'}`);
    console.log(`   - completeDataFile: ${adminApp.hasOwnProperty('completeDataFile') ? '✅' : '❌'}`);

    if (!adminApp.hasOwnProperty('actualSampleCount') || 
        !adminApp.hasOwnProperty('actualProteinCount') || 
        !adminApp.hasOwnProperty('previewDataFile') || 
        !adminApp.hasOwnProperty('completeDataFile')) {
      console.log('❌ 管理员API缺少新字段');
      allPassed = false;
    } else {
      console.log('✅ 管理员API包含所有新字段');
    }

    // 3. 测试管理员API - 获取单个申请详情
    console.log('\n3. 测试管理员API - 获取单个申请详情...');
    const adminAppDetailResponse = await axios.get(`${API_BASE_URL}/api/admin/applications/${adminApp.id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    if (adminAppDetailResponse.status !== 200) {
      console.log('❌ 管理员API获取申请详情失败');
      allPassed = false;
    } else {
      const adminAppDetail = adminAppDetailResponse.data.application;
      console.log('✅ 管理员API申请详情结构检查:');
      console.log(`   - actualSampleCount: ${adminAppDetail.hasOwnProperty('actualSampleCount') ? '✅' : '❌'}`);
      console.log(`   - actualProteinCount: ${adminAppDetail.hasOwnProperty('actualProteinCount') ? '✅' : '❌'}`);
      console.log(`   - previewDataFile: ${adminAppDetail.hasOwnProperty('previewDataFile') ? '✅' : '❌'}`);
      console.log(`   - completeDataFile: ${adminAppDetail.hasOwnProperty('completeDataFile') ? '✅' : '❌'}`);

      if (!adminAppDetail.hasOwnProperty('actualSampleCount') || 
          !adminAppDetail.hasOwnProperty('actualProteinCount') || 
          !adminAppDetail.hasOwnProperty('previewDataFile') || 
          !adminAppDetail.hasOwnProperty('completeDataFile')) {
        console.log('❌ 管理员API申请详情缺少新字段');
        allPassed = false;
      } else {
        console.log('✅ 管理员API申请详情包含所有新字段');
      }
    }

    // 4. 创建测试用户并测试用户API
    console.log('\n4. 创建测试用户...');
    const testUserEmail = `testuser${Date.now()}@example.com`;
    const testUserPassword = 'testpass123';

    try {
      await axios.post(`${API_BASE_URL}/api/auth/register`, {
        email: testUserEmail,
        password: testUserPassword,
        firstName: 'Test',
        lastName: 'User'
      });
      console.log('✅ 测试用户创建成功');
    } catch (error) {
      console.log(`❌ 创建测试用户失败: ${error.response?.data?.message || error.message}`);
      allPassed = false;
      return allPassed;
    }

    // 5. 用户登录
    console.log('\n5. 测试用户登录...');
    const userLoginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: testUserEmail,
      password: testUserPassword
    });

    if (userLoginResponse.status !== 200) {
      console.log(`❌ 用户登录失败: ${userLoginResponse.status}`);
      allPassed = false;
      return allPassed;
    }

    console.log('✅ 用户登录成功');
    const userToken = userLoginResponse.data.token;

    // 6. 测试用户API - 获取申请列表
    console.log('\n6. 测试用户API - 获取申请列表...');
    const userAppsResponse = await axios.get(`${API_BASE_URL}/api/applications`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    if (userAppsResponse.status !== 200) {
      console.log(`❌ 用户API获取申请列表失败: ${userAppsResponse.status}`);
      allPassed = false;
    } else {
      console.log(`✅ 用户API获取申请列表成功，申请数量: ${userAppsResponse.data.applications.length}`);
      
      // 检查API结构（即使没有申请，也要检查结构）
      if (userAppsResponse.data.applications.length > 0) {
        const userApp = userAppsResponse.data.applications[0];
        console.log('✅ 用户API申请列表结构检查:');
        console.log(`   - actualSampleCount: ${userApp.hasOwnProperty('actualSampleCount') ? '✅' : '❌'}`);
        console.log(`   - actualProteinCount: ${userApp.hasOwnProperty('actualProteinCount') ? '✅' : '❌'}`);
        console.log(`   - previewDataFile: ${userApp.hasOwnProperty('previewDataFile') ? '✅' : '❌'}`);
        console.log(`   - completeDataFile: ${userApp.hasOwnProperty('completeDataFile') ? '✅' : '❌'}`);

        if (!userApp.hasOwnProperty('actualSampleCount') || 
            !userApp.hasOwnProperty('actualProteinCount') || 
            !userApp.hasOwnProperty('previewDataFile') || 
            !userApp.hasOwnProperty('completeDataFile')) {
          console.log('❌ 用户API申请列表缺少新字段');
          allPassed = false;
        } else {
          console.log('✅ 用户API申请列表包含所有新字段');
        }
      } else {
        console.log('⚠️ 用户没有申请，无法检查申请列表结构');
      }
    }

    // 7. 创建一个测试申请给用户
    console.log('\n7. 为用户创建测试申请...');
    try {
      const createAppResponse = await axios.post(`${API_BASE_URL}/api/applications`, {
        contactInfo: { phone: '************' },
        proteinPanel: 'olink',
        proteinPanelOption: 'Olink Explore 1536',
        sampleCount: 10,
        proteinDataFiles: [{
          originalName: 'test-data.txt',
          filename: 'test-data.txt',
          path: '/uploads/test-data.txt',
          size: 1024,
          fileType: 'text/plain'
        }],
        groupingFiles: [],
        projectDetails: {
          title: 'Test Project',
          description: 'Test Description',
          objectives: 'Test Objectives'
        },
        sampleTypes: ['plasma'],
        contractNumber: 'TEST001'
      }, {
        headers: { Authorization: `Bearer ${userToken}` }
      });

      if (createAppResponse.status === 201) {
        console.log('✅ 测试申请创建成功');
        const newAppId = createAppResponse.data.application.id;

        // 8. 测试用户API - 获取单个申请详情
        console.log('\n8. 测试用户API - 获取单个申请详情...');
        const userAppDetailResponse = await axios.get(`${API_BASE_URL}/api/applications/${newAppId}`, {
          headers: { Authorization: `Bearer ${userToken}` }
        });

        if (userAppDetailResponse.status !== 200) {
          console.log(`❌ 用户API获取申请详情失败: ${userAppDetailResponse.status}`);
          allPassed = false;
        } else {
          const userAppDetail = userAppDetailResponse.data.application;
          console.log('✅ 用户API申请详情结构检查:');
          console.log(`   - actualSampleCount: ${userAppDetail.hasOwnProperty('actualSampleCount') ? '✅' : '❌'}`);
          console.log(`   - actualProteinCount: ${userAppDetail.hasOwnProperty('actualProteinCount') ? '✅' : '❌'}`);
          console.log(`   - previewDataFile: ${userAppDetail.hasOwnProperty('previewDataFile') ? '✅' : '❌'}`);
          console.log(`   - completeDataFile: ${userAppDetail.hasOwnProperty('completeDataFile') ? '✅' : '❌'}`);

          if (!userAppDetail.hasOwnProperty('actualSampleCount') || 
              !userAppDetail.hasOwnProperty('actualProteinCount') || 
              !userAppDetail.hasOwnProperty('previewDataFile') || 
              !userAppDetail.hasOwnProperty('completeDataFile')) {
            console.log('❌ 用户API申请详情缺少新字段');
            allPassed = false;
          } else {
            console.log('✅ 用户API申请详情包含所有新字段');
          }
        }
      } else {
        console.log(`❌ 创建测试申请失败: ${createAppResponse.status}`);
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ 创建测试申请失败: ${error.response?.data?.message || error.message}`);
      allPassed = false;
    }

    if (allPassed) {
      console.log('\n🎉 所有API结构测试通过！');
      console.log('📋 测试结果:');
      console.log('   ✅ 管理员API包含所有新字段');
      console.log('   ✅ 用户API包含所有新字段');
      console.log('   ✅ 前端可以正确显示报告完成数据');
    } else {
      console.log('\n❌ 部分API结构测试失败');
    }

  } catch (error) {
    console.log(`\n❌ 测试过程中发生错误: ${error.message}`);
    if (error.response) {
      console.log(`API响应: ${error.response.status} ${JSON.stringify(error.response.data, null, 2)}`);
    }
    allPassed = false;
  }

  return allPassed;
}

// 运行测试
if (require.main === module) {
  testAPIStructure().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testAPIStructure };

<template>
  <BaseCard
    variant="outlined"
    padding="lg"
    class="contact-info-section"
  >
    <template #header>
      <h3 class="section-title">
        📞 {{ $t('contactInformation') }}
      </h3>
    </template>

    <div class="form-grid">
      <!-- 联系电话（必填） -->
      <FormField
        v-model="localData.phone"
        type="tel"
        :label="$t('contactPhone')"
        :placeholder="$t('contactPhonePlaceholder')"
        :required="true"
        :error="errors.phone"
        @change="handleChange"
      />

      <!-- 联系人名字（可选） -->
      <FormField
        v-model="localData.contactName"
        type="text"
        :label="$t('contactNameOptional')"
        :placeholder="$t('contactNamePlaceholder')"
        @change="handleChange"
      />

      <!-- 联系人单位（可选） -->
      <FormField
        v-model="localData.contactOrganization"
        type="text"
        :label="$t('contactOrganizationOptional')"
        :placeholder="$t('contactOrganizationPlaceholder')"
        @change="handleChange"
      />
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseCard, FormField } from '@/components/ui'

interface ContactInfo {
  phone: string
  contactName: string
  contactOrganization: string
}

interface Props {
  modelValue: ContactInfo
  errors?: Record<string, string>
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: ContactInfo]
  change: [value: ContactInfo]
}>()

const { t } = useI18n()

const localData = reactive<ContactInfo>({
  phone: props.modelValue.phone || '',
  contactName: props.modelValue.contactName || '',
  contactOrganization: props.modelValue.contactOrganization || ''
})

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(localData, newValue)
}, { deep: true })

const handleChange = () => {
  const updatedData = { ...localData }
  emit('update:modelValue', updatedData)
  emit('change', updatedData)
}
</script>

<style scoped>
.contact-info-section {
  margin-bottom: 2rem;
}

.section-title {
  margin: 0;
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-grid {
  display: grid;
  gap: 1.5rem;
}

/* 响应式网格 */
@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .form-grid :first-child {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1024px) {
  .form-grid {
    grid-template-columns: 2fr 1fr 1fr;
  }
  
  .form-grid :first-child {
    grid-column: auto;
  }
}
</style>

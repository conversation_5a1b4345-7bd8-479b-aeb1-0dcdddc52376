<template>
  <div class="report-section">
    <h4 class="report-section__title">
      📊 {{ $t('analysisReport') }}
    </h4>

    <!-- 报告卡片网格 -->
    <div class="reports-grid">
      <!-- 预览报告 -->
      <BaseCard
        v-if="previewReport"
        variant="default"
        padding="md"
        hoverable
        class="report-card preview-report"
      >
        <template #header>
          <div class="report-header">
            <h5 class="report-title">📄 {{ $t('previewReport') }}</h5>
            <StatusBadge status="free" size="sm">{{ $t('free') }}</StatusBadge>
          </div>
        </template>

        <div class="report-details">
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileName') }}:</span>
            <span class="detail-value">{{ previewReport.originalName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('fileSize') }}:</span>
            <span class="detail-value">{{ formatFileSize(previewReport.size) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">{{ $t('generationTime') }}:</span>
            <span class="detail-value">{{ formatDate(previewReport.uploadedAt) }}</span>
          </div>
        </div>

        <template #footer>
          <BaseButton
            variant="success"
            size="md"
            @click="downloadReport('preview')"
            :loading="downloading === 'preview'"
          >
            📥 {{ $t('downloadPreviewReport') }}
          </BaseButton>
        </template>
      </BaseCard>

      <!-- 完整报告 -->
      <BaseCard
        v-if="fullReport"
        variant="default"
        padding="md"
        hoverable
        class="report-card full-report"
      >
      <template #header>
        <div class="report-header">
          <h5 class="report-title">📊 {{ $t('fullReport') }}</h5>
          <StatusBadge 
            :status="paymentStatus" 
            size="sm"
          >
            {{ getPaymentStatusText(paymentStatus) }}
          </StatusBadge>
        </div>
      </template>

      <div class="report-details">
        <div class="detail-item">
          <span class="detail-label">{{ $t('fileName') }}:</span>
          <span class="detail-value">{{ fullReport.originalName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">{{ $t('fileSize') }}:</span>
          <span class="detail-value">{{ formatFileSize(fullReport.size) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">{{ $t('price') }}:</span>
          <span class="detail-value price">${{ reportPrice || 0 }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">{{ $t('generationTime') }}:</span>
          <span class="detail-value">{{ formatDate(fullReport.uploadedAt) }}</span>
        </div>
      </div>

      <template #footer>
        <BaseButton
          v-if="paymentStatus === 'paid' || paymentStatus === 'free'"
          variant="success"
          size="md"
          @click="downloadReport('full')"
          :loading="downloading === 'full'"
        >
          💰 {{ $t('downloadFullReport') }}
        </BaseButton>
        <BaseButton
          v-else
          variant="primary"
          size="md"
          @click="initiatePayment"
          :loading="downloading === 'payment'"
        >
          💳 {{ $t('payForDownload') }} (${{ reportPrice || 0 }})
        </BaseButton>
      </template>
    </BaseCard>
    </div> <!-- 结束 reports-grid -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { BaseButton, BaseCard, StatusBadge } from '@/components/ui'

interface ReportFile {
  originalName: string
  size: number
  uploadedAt: string
}

interface Props {
  previewReport?: ReportFile
  fullReport?: ReportFile
  paymentStatus?: 'paid' | 'unpaid' | 'free' | 'pending'
  reportPrice?: number
  applicationId: number
}

const props = withDefaults(defineProps<Props>(), {
  paymentStatus: 'unpaid',
  reportPrice: 0
})

const emit = defineEmits<{
  downloadReport: [applicationId: number, type: 'preview' | 'full']
  initiatePayment: [applicationId: number, price: number]
}>()

const { t } = useI18n()
const downloading = ref<string | null>(null)

const downloadReport = async (type: 'preview' | 'full') => {
  downloading.value = type
  try {
    emit('downloadReport', props.applicationId, type)
  } finally {
    downloading.value = null
  }
}

const initiatePayment = () => {
  downloading.value = 'payment'
  emit('initiatePayment', props.applicationId, props.reportPrice || 0)
  downloading.value = null
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getPaymentStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    pending: '待支付',
    paid: '已支付',
    free: '免费',
    unpaid: '待支付',
    failed: '支付失败'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.report-section {
  margin: 1.5rem 0;
}

.report-section__title {
  color: #2d3748;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 报告网格布局 */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.report-card {
  height: fit-content;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .reports-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.report-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.report-title {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.report-details {
  display: grid;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
}

.detail-value {
  color: #2d3748;
  font-size: 0.875rem;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.detail-value.price {
  font-weight: 700;
  color: #667eea;
  font-size: 1rem;
}
</style>

<template>
  <div :class="loadingClasses">
    <div class="loading-state__content">
      <!-- Spinner -->
      <div v-if="type === 'spinner'" class="loading-spinner">
        <div class="spinner-ring"></div>
      </div>
      
      <!-- Dots -->
      <div v-else-if="type === 'dots'" class="loading-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>
      
      <!-- Pulse -->
      <div v-else-if="type === 'pulse'" class="loading-pulse">
        <div class="pulse-circle"></div>
      </div>
      
      <!-- Skeleton -->
      <div v-else-if="type === 'skeleton'" class="loading-skeleton">
        <div class="skeleton-line skeleton-line--title"></div>
        <div class="skeleton-line skeleton-line--text"></div>
        <div class="skeleton-line skeleton-line--text"></div>
        <div class="skeleton-line skeleton-line--short"></div>
      </div>
      
      <!-- Text -->
      <div v-if="text" class="loading-state__text">
        {{ text }}
      </div>
      
      <!-- Description -->
      <div v-if="description" class="loading-state__description">
        {{ description }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  size?: 'sm' | 'md' | 'lg'
  text?: string
  description?: string
  overlay?: boolean
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'md',
  overlay: false,
  fullscreen: false
})

const loadingClasses = computed(() => [
  'loading-state',
  `loading-state--${props.size}`,
  {
    'loading-state--overlay': props.overlay,
    'loading-state--fullscreen': props.fullscreen
  }
])
</script>

<style scoped>
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-state--overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  z-index: 10;
}

.loading-state--fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.loading-state__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-state__text {
  font-weight: 600;
  color: #4a5568;
  text-align: center;
}

.loading-state__description {
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
  max-width: 20rem;
  line-height: 1.5;
}

/* Size variants */
.loading-state--sm .loading-state__text {
  font-size: 0.875rem;
}

.loading-state--md .loading-state__text {
  font-size: 1rem;
}

.loading-state--lg .loading-state__text {
  font-size: 1.125rem;
}

/* Spinner */
.loading-spinner {
  position: relative;
}

.spinner-ring {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-state--sm .spinner-ring {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 2px;
}

.loading-state--lg .spinner-ring {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* Dots */
.loading-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #667eea;
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-state--sm .dot {
  width: 0.375rem;
  height: 0.375rem;
}

.loading-state--lg .dot {
  width: 0.75rem;
  height: 0.75rem;
}

/* Pulse */
.loading-pulse {
  position: relative;
}

.pulse-circle {
  width: 2rem;
  height: 2rem;
  background: #667eea;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.loading-state--sm .pulse-circle {
  width: 1.5rem;
  height: 1.5rem;
}

.loading-state--lg .pulse-circle {
  width: 3rem;
  height: 3rem;
}

/* Skeleton */
.loading-skeleton {
  width: 100%;
  max-width: 20rem;
}

.skeleton-line {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.skeleton-line--title {
  height: 1.5rem;
  width: 60%;
}

.skeleton-line--text {
  width: 100%;
}

.skeleton-line--short {
  width: 40%;
  margin-bottom: 0;
}

.loading-state--sm .skeleton-line {
  height: 0.75rem;
}

.loading-state--sm .skeleton-line--title {
  height: 1rem;
}

.loading-state--lg .skeleton-line {
  height: 1.25rem;
}

.loading-state--lg .skeleton-line--title {
  height: 2rem;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>

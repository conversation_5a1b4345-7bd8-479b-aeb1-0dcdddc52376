/**
 * Environment variable utilities for cross-platform compatibility
 * Handles boolean and numeric environment variables consistently across Windows, Linux, and macOS
 */

/**
 * Parse boolean environment variable
 * Supports: true, "true", "1", "yes", "on" (case insensitive)
 * @param {string} value - Environment variable value
 * @param {boolean} defaultValue - Default value if parsing fails
 * @returns {boolean}
 */
function parseBoolean(value, defaultValue = false) {
  if (value === undefined || value === null) {
    return defaultValue;
  }

  const stringValue = String(value).toLowerCase().trim();
  return ['true', '1', 'yes', 'on'].includes(stringValue);
}

/**
 * Parse integer environment variable
 * @param {string} value - Environment variable value
 * @param {number} defaultValue - Default value if parsing fails
 * @returns {number}
 */
function parseInteger(value, defaultValue = 0) {
  if (value === undefined || value === null) {
    return defaultValue;
  }

  const parsed = parseInt(String(value), 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse float environment variable
 * @param {string} value - Environment variable value
 * @param {number} defaultValue - Default value if parsing fails
 * @returns {number}
 */
function parseFloat(value, defaultValue = 0.0) {
  if (value === undefined || value === null) {
    return defaultValue;
  }

  const parsed = Number.parseFloat(String(value));
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse string environment variable
 * @param {string} value - Environment variable value
 * @param {string} defaultValue - Default value if undefined
 * @returns {string}
 */
function parseString(value, defaultValue = '') {
  if (value === undefined || value === null) {
    return defaultValue;
  }

  return String(value).trim();
}

/**
 * Parse file size string (supports KB, MB, GB suffixes)
 * @param {string} value - Environment variable value (e.g., "100MB", "1GB", "1024")
 * @param {number} defaultValue - Default value in bytes
 * @returns {number} Size in bytes
 */
function parseFileSize(value, defaultValue = 100 * 1024 * 1024) {
  if (value === undefined || value === null) {
    return defaultValue;
  }

  const stringValue = String(value).trim().toUpperCase();

  // If it's just a number, treat as bytes
  if (/^\d+$/.test(stringValue)) {
    return parseInt(stringValue, 10);
  }

  // Parse with suffix
  const match = stringValue.match(/^(\d+(?:\.\d+)?)(KB|MB|GB|TB)?$/);
  if (!match) {
    return defaultValue;
  }

  const [, numberPart, suffix] = match;
  const number = parseFloat(numberPart);

  switch (suffix) {
  case 'KB':
    return Math.round(number * 1024);
  case 'MB':
    return Math.round(number * 1024 * 1024);
  case 'GB':
    return Math.round(number * 1024 * 1024 * 1024);
  case 'TB':
    return Math.round(number * 1024 * 1024 * 1024 * 1024);
  default:
    return Math.round(number);
  }
}

/**
 * Get environment configuration with proper type conversion
 * @returns {object} Parsed environment configuration
 */
function getEnvConfig() {
  return {
    // Server configuration
    NODE_ENV: parseString(process.env.NODE_ENV, 'development'),
    PORT: parseInteger(process.env.PORT, 3001),
    FRONTEND_URL: parseString(process.env.FRONTEND_URL, 'http://localhost:5173'),

    // Database configuration
    USE_MYSQL: parseBoolean(process.env.USE_MYSQL, false),
    DB_HOST: parseString(process.env.DB_HOST, 'localhost'),
    DB_PORT: parseInteger(process.env.DB_PORT, 3306),
    DB_NAME: parseString(process.env.DB_NAME, 'quantix'),
    DB_USER: parseString(process.env.DB_USER, 'root'),
    DB_PASSWORD: parseString(process.env.DB_PASSWORD, ''),
    DB_DIALECT: parseString(process.env.DB_DIALECT, 'sqlite'),

    // JWT configuration
    JWT_SECRET: parseString(process.env.JWT_SECRET, 'your-super-secret-jwt-key-here'),
    JWT_EXPIRE: parseString(process.env.JWT_EXPIRE, '7d'),

    // Email configuration
    EMAIL_SERVICE: parseString(process.env.EMAIL_SERVICE, 'gmail'),
    EMAIL_USER: parseString(process.env.EMAIL_USER, ''),
    EMAIL_PASS: parseString(process.env.EMAIL_PASS, ''),
    EMAIL_TEST_MODE: parseString(process.env.EMAIL_TEST_MODE, ''),

    // File upload configuration
    MAX_FILE_SIZE: parseFileSize(process.env.MAX_FILE_SIZE, 100 * 1024 * 1024),
    UPLOAD_PATH: parseString(process.env.UPLOAD_PATH, './uploads'),

    // AI service configuration
    OPENAI_API_KEY: parseString(process.env.OPENAI_API_KEY, ''),
    OPENAI_MODEL: parseString(process.env.OPENAI_MODEL, 'gpt-3.5-turbo'),

    // Payment configuration
    STRIPE_SECRET_KEY: parseString(process.env.STRIPE_SECRET_KEY, ''),
    STRIPE_PUBLISHABLE_KEY: parseString(process.env.STRIPE_PUBLISHABLE_KEY, '')
  };
}

module.exports = {
  parseBoolean,
  parseInteger,
  parseFloat: parseFloat,
  parseString,
  parseFileSize,
  getEnvConfig
};
